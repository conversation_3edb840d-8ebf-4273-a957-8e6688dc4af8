## 产品功能需求：会话后台持续生成与状态保持

### 功能概述
实现AI对话在用户切换会话时的后台持续生成能力，确保用户返回原会话时能够查看到完整的回答内容。

### 核心场景描述
1. **触发条件**：用户发起新对话请求，AI开始生成回答并显示"思考中..."状态
2. **中断行为**：用户在AI回答生成过程中主动切换到其他历史会话
3. **后台处理**：AI在后台继续完成原会话的内容生成，不因用户离开而中断
4. **恢复体验**：用户重新切回原会话时，能够看到AI已完成的完整回答内容

### 技术实现要求

**会话状态管理**
- 维护每个会话的独立生成状态
- 支持多会话并行处理能力
- 实现会话切换时的状态保存与恢复

**后台生成机制**
- AI生成进程与前端展示解耦
- 用户离开会话时，后台继续执行生成任务
- 生成完成后自动保存到对应会话记录中

**用户界面交互**
- 切换会话时保持原会话的生成状态标识
- 返回会话时智能判断内容更新状态
- 提供视觉提示表明内容已在后台更新完成

### 预期用户体验
用户无需等待AI完整回答完成即可自由切换查看其他会话，当返回原会话时能够获得完整的AI回答，提升多任务处理效率和使用流畅度。