import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  getHistory,
  getRecords,
  saveRecord,
  type SessionRecord,
} from "@/services/chatService";

export interface CurrentFile {
  id: number;
  name: string;
  size: string;
  type: string;
  fileType: "PDF" | "DOC" | "DOCX" | "XLS" | "XLSX" | "PPT" | "PPTX" | "PNG" | "JPG" | "JPEG";
  attachmentText: string;
}

// 唯一ID生成器
let messageIdCounter = Date.now();
const generateUniqueId = () => {
  return ++messageIdCounter;
};

export interface Message {
  id: number;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  thinkingProcess?: {
    content: string;
    isStreaming?: boolean;
    isComplete?: boolean;
    isStopped?: boolean;
    duration?: number;
  };
  isStreaming?: boolean;
  streamingContent?: string;
}

export interface ChatHistory {
  id?: string;
  sessionTitle: string;
  firstQuestion?: string;
  data?: string;
  createBy?: string;
  createTime?: string;
  updateTime?: string;
}

export interface UserInfo {
  account?: string;
  deptId?: string;
  deptName?: string;
  email?: string;
  id?: number;
  avatar?: string;
  gender?: number;
  staffNumber?: string;
  realName?: string;
  nickName?: string;
  unionId?: string;
}

export interface HistoryApiResponseItem {
  id: string;
  sessionTitle: string;
  firstQuestion: string;
  data?: string;
  createTime: string;
}

export interface HistoryApiResponse {
  data: HistoryApiResponseItem[];
}

interface ChatState {
  // 聊天消息
  messages: Message[];

  // 聊天历史
  chatHistory: ChatHistory[];

  // 当前聊天ID
  currentChatId: string;

  // 输入状态
  inputValue: string;

  // 加载状态
  isLoading: boolean;

  // 侧边栏状态
  sidebarCollapsed: boolean;

  // 会话是否开始
  isConversationStarted: boolean;

  // 当前会话title
  currentTitle: string;

  // 深度思考开关状态
  isDeepMindEnabled: boolean;

  // 流式数据状态
  currentStreamingMessage?: {
    id: number;
    thinkingContent: string;
    answerContent: string; // 累积的完整答案
    currentMessageContent: string; // 当前正在显示的单个Message内容
    isThinkingComplete: boolean;
    isThinkingStreamComplete: boolean; // 思考数据流是否完成（区别于打字机完成）
    isReadyForAnswers: boolean; // 是否准备好处理答案（思考过程打字机完成后）
    isAnswerComplete: boolean;
    answerStarted: boolean; // 标记答案是否已开始显示
    tempMessagesCache: string[]; // 暂存Messages数据，等待思考过程完成
    pendingMessages: string[]; // 缓存的Messages数据队列
    isProcessingMessages: boolean; // 是否正在处理Messages
    currentMessageComplete: boolean; // 当前Message的打字机是否完成
    isStopped: boolean; // 是否已停止
    startTime: number; // 整体开始时间
    stopTime?: number; // 整体停止时间
    thinkingStartTime?: number; // 思考开始时间
    thinkingStopTime?: number; // 思考停止时间
    answerStartTime?: number; // 回答开始时间
    answerStopTime?: number; // 回答停止时间
  };

  // 用户信息
  userInfo: UserInfo;

  // 文件上传状态
  uploadResult: CurrentFile[];

  // Actions
  setMessages: (messages: Message[]) => void;
  addMessage: (
    message: Omit<Message, "id" | "timestamp"> & { id?: number }
  ) => void;
  updateMessage: (id: number, updates: Partial<Message>) => void;
  deleteMessage: (id: number) => void;
  clearMessages: () => void;

  setChatHistory: () => void;

  setCurrentChatId: (id: string) => void;
  setInputValue: (value: string) => void;
  setIsLoading: (loading: boolean) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setIsConversationStarted: (started: boolean) => void;

  setCurrentTitle: (title: string) => void;

  // 深度思考状态操作
  setIsDeepMindEnabled: (enabled: boolean) => void;

  // 流式数据处理方法
  startStreamingMessage: (userMessage: string) => void;
  updateThinkingStream: (content: string) => void;
  completeThinkingStream: () => void;
  updateAnswerStream: (content: string) => void;
  completeAnswerStream: () => void;

  // Messages队列管理方法
  addTempMessage: (content: string) => void; // 新增：暂存Messages到缓存
  startAnswerProcessing: () => void; // 新增：开始处理答案
  addPendingMessage: (content: string) => void;
  startProcessingMessages: () => void;
  completeMessageProcessing: () => void;
  completeCurrentMessage: () => void;
  startNextMessage: () => void;

  // 停止控制方法
  stopStreamingMessage: () => void;

  startNewChat: () => void;

  loadHistory: () => void;

  loadChatSession: (sessionId: string) => Promise<void>;

  // 设置用户信息
  setUserInfo: (userInfo: UserInfo) => void;
  getUserInfo: () => UserInfo;

  setUploadResult: (files: CurrentFile[]) => void;
  addUploadFiles: (files: CurrentFile[]) => void;
  removeUploadFile: (id: number) => void;
  clearUploadResult: () => void;
}

export const useChatStore = create<ChatState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        messages: [],

        chatHistory: [],

        currentChatId: "",
        inputValue: "",
        isLoading: false,
        sidebarCollapsed: false,
        isConversationStarted: true,
        currentStreamingMessage: undefined,
        currentTitle: "",
        isDeepMindEnabled: false,
        userInfo: {},
        uploadResult: [],
        // Actions
        setUserInfo: (userInfo) => set({ userInfo }),
        getUserInfo: () => get().userInfo,

        setUploadResult: (files) => set({ uploadResult: files }),
        addUploadFiles: (files) => set((state) => ({ 
          uploadResult: [...state.uploadResult, ...files] 
        })),
        removeUploadFile: (id) => set((state) => ({ 
          uploadResult: state.uploadResult.filter((file) => file.id !== id) 
        })),
        clearUploadResult: () => set({ uploadResult: [] }),

        setMessages: (messages) => set({ messages }),

        setCurrentTitle: (currentTitle) => set({ currentTitle }),

        addMessage: (messageData) => {
          const newMessage: Message = {
            ...messageData,
            id: messageData.id || generateUniqueId(),
            timestamp: new Date(),
          };

          set((state) => ({
            messages: [...state.messages, newMessage],
            isConversationStarted: true,
          }));
        },

        updateMessage: (id, updates) => {
          set((state) => ({
            messages: state.messages.map((msg) =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          }));
        },

        deleteMessage: (id) => {
          set((state) => ({
            messages: state.messages.filter((msg) => msg.id !== id),
          }));
        },

        clearMessages: () => {
          set({
            messages: [],
            isConversationStarted: false,
          });
        },

        setCurrentChatId: (id) => set({ currentChatId: id }),
        setInputValue: (value) => set({ inputValue: value }),
        setIsLoading: (loading) => set({ isLoading: loading }),
        setSidebarCollapsed: (collapsed) =>
          set({ sidebarCollapsed: collapsed }),
        setIsConversationStarted: (started) =>
          set({ isConversationStarted: started }),

        // 深度思考状态操作
        setIsDeepMindEnabled: (enabled) => set({ isDeepMindEnabled: enabled }),

        // 流式数据处理方法
        startStreamingMessage: (userMessage: string) => {
          const { addMessage } = get();

          // 预生成唯一ID
          const userMessageId = generateUniqueId();
          const assistantMessageId = generateUniqueId();

          // 立即设置当前流式消息状态，触发isStreaming = true
          const now = Date.now();
          set({
            currentStreamingMessage: {
              id: assistantMessageId,
              thinkingContent: "",
              answerContent: "",
              currentMessageContent: "",
              isThinkingComplete: false,
              isThinkingStreamComplete: false,
              isReadyForAnswers: false,
              isAnswerComplete: false,
              answerStarted: false,
              tempMessagesCache: [],
              pendingMessages: [],
              isProcessingMessages: false,
              currentMessageComplete: false,
              isStopped: false,
              startTime: now,
              thinkingStartTime: now,
            },
          });

          // 延迟添加消息到数组，给loading效果留出显示时间
          setTimeout(() => {
            // 添加用户消息，使用预设ID
            addMessage({
              id: userMessageId,
              type: "user",
              content: userMessage,
            });

            // 创建流式assistant消息，使用预设ID
            addMessage({
              id: assistantMessageId,
              type: "assistant",
              content: "",
              isStreaming: true,
              streamingContent: "",
              thinkingProcess: {
                content: "",
                isStreaming: true,
                isComplete: false,
              },
            });
          }, 300); // 延迟300ms，给loading状态足够的显示时间
        },

        updateThinkingStream: (content: string) => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          // 累积思考内容
          const updatedThinkingContent =
            currentStreamingMessage.thinkingContent + content;

          // 更新流式消息状态
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              thinkingContent: updatedThinkingContent,
            },
          });

          // 更新消息列表中的thinking process
          set({
            messages: messages.map((msg) =>
              msg.id === currentStreamingMessage.id
                ? {
                    ...msg,
                    thinkingProcess: {
                      content: updatedThinkingContent,
                      isStreaming: true,
                      isComplete: false,
                    },
                  }
                : msg
            ),
          });
        },

        completeThinkingStream: () => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          console.log("完成思考过程，当前状态:", {
            thinkingContent: currentStreamingMessage.thinkingContent,
            thinkingContentLength:
              currentStreamingMessage.thinkingContent.length,
            tempMessagesCount: currentStreamingMessage.tempMessagesCache.length,
            isThinkingComplete: currentStreamingMessage.isThinkingComplete,
          });

          // 检查思考内容是否存在
          if (!currentStreamingMessage.thinkingContent) {
            console.log("没有思考内容，跳过思考过程完成");
            return;
          }

          // 更新流式消息状态 - 完成思考过程相关状态
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isThinkingComplete: true,
              isThinkingStreamComplete: true,
              thinkingStopTime: Date.now(),
            },
          });

          // 更新消息列表中的thinking process为完成状态
          set({
            messages: messages.map((msg) =>
              msg.id === currentStreamingMessage.id
                ? {
                    ...msg,
                    thinkingProcess: {
                      content: currentStreamingMessage.thinkingContent,
                      isStreaming: false,
                      isComplete: true,
                    },
                  }
                : msg
            ),
          });

          console.log("思考过程状态更新完成，立即开始答案处理");
          
          // 立即启动答案处理，不等待打字机完成
          get().startAnswerProcessing();
        },

        updateAnswerStream: (content: string) => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          if (currentStreamingMessage.isProcessingMessages) {
            console.log(
              "队列模式：处理Message内容:",
              content.substring(0, 50) + "..."
            );

            // 队列处理模式：直接累积内容，立即处理下一个
            const newAnswerContent =
              currentStreamingMessage.answerContent + content;

            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                answerContent: newAnswerContent,
                currentMessageContent: "", // 清空当前内容
                currentMessageComplete: true,
              },
            });

            // 更新消息显示
            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: newAnswerContent,
                      streamingContent: newAnswerContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });

            // 立即处理下一个Message
            console.log("队列模式：立即处理下一个Message");
            setTimeout(() => {
              get().startNextMessage();
            }, 0);
          } else {
            // 普通流式处理：累积内容
            const newContent = currentStreamingMessage.answerContent + content;

            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                answerContent: newContent,
              },
            });

            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: newContent,
                      streamingContent: newContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });
          }
        },

        completeAnswerStream: async () => {
          const { currentStreamingMessage, messages, currentChatId } = get();
          if (!currentStreamingMessage || !currentChatId) return;

          // Find the user message that triggered this response
          const userMessageIndex =
            messages.findIndex((msg) => msg.id === currentStreamingMessage.id) -
            1;
          const userMessage =
            userMessageIndex >= 0 ? messages[userMessageIndex] : null;

          const assistantMessage = messages.find(
            (msg) => msg.id === currentStreamingMessage.id
          );

          // Save the record to the backend
          try {
            if (
              userMessage &&
              userMessage.type === "user" &&
              assistantMessage
            ) {
              // Construct the final assistant message object
              const finalAssistantMessage: Message = {
                ...assistantMessage,
                content: currentStreamingMessage.answerContent,
                isStreaming: false,
                streamingContent: undefined,
                thinkingProcess: {
                  ...(assistantMessage.thinkingProcess || { content: "" }),
                  content: currentStreamingMessage.thinkingContent,
                  isStreaming: false,
                  isComplete: true,
                  isStopped: currentStreamingMessage.isStopped,
                  duration:
                    assistantMessage.thinkingProcess?.duration ||
                    currentStreamingMessage.thinkingStopTime,
                },
              };

              const recordData: SessionRecord = {
                sessionId: `${currentChatId}`,
                question: JSON.stringify(userMessage),
                answer: JSON.stringify(finalAssistantMessage),
              };
              await saveRecord(recordData).send();
              console.log("Chat record saved successfully.");
            }
          } catch (error) {
            console.error("Failed to save chat record:", error);
          }

          // 更新消息列表为完成状态
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isAnswerComplete: true,
            },
            messages: messages.map((msg) =>
              msg.id === currentStreamingMessage.id
                ? {
                    ...msg,
                    content: currentStreamingMessage.answerContent,
                    isStreaming: false,
                    streamingContent: undefined,
                    thinkingProcess: currentStreamingMessage.isStopped
                      ? msg.thinkingProcess // Preserve existing state on stop
                      : {
                          ...(msg.thinkingProcess || { content: "" }),
                          content: currentStreamingMessage.thinkingContent,
                          isStreaming: false,
                          isComplete: true,
                        },
                    isAnswerComplete: true,
                  }
                : msg
            ),
          });

          // 清除当前流式消息状态
          console.log("Final messages state:", get().messages);
          set({
            currentStreamingMessage: undefined,
          });
        },

        // Messages队列管理方法
        addTempMessage: (content: string) => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          console.log("添加Messages数据:", {
            content: content.substring(0, 50) + "...",
            answerStarted: currentStreamingMessage.answerStarted,
            isReadyForAnswers: currentStreamingMessage.isReadyForAnswers,
          });

          // 如果答案已经开始显示，直接实时添加到答案内容
          if (currentStreamingMessage.answerStarted && currentStreamingMessage.isReadyForAnswers) {
            console.log("答案已开始，实时添加Messages内容");
            const newAnswerContent = currentStreamingMessage.answerContent + content;
            
            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                answerContent: newAnswerContent,
              },
            });

            // 立即更新UI显示
            const { messages } = get();
            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: newAnswerContent,
                      streamingContent: newAnswerContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });
          } else {
            // 答案尚未开始，暂存到缓存
            console.log("答案未开始，暂存到缓存");
            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                tempMessagesCache: [
                  ...currentStreamingMessage.tempMessagesCache,
                  content,
                ],
              },
            });
          }
        },

        startAnswerProcessing: () => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) {
            console.error(
              "startAnswerProcessing: currentStreamingMessage不存在"
            );
            return;
          }

          console.log("开始答案处理，立即显示已缓存的内容:", {
            cacheLength: currentStreamingMessage.tempMessagesCache.length,
            isThinkingComplete: currentStreamingMessage.isThinkingComplete,
            isReadyForAnswers: currentStreamingMessage.isReadyForAnswers,
          });

          // 检查是否已经在处理中
          if (currentStreamingMessage.isReadyForAnswers) {
            console.log("答案处理已经启动，跳过重复调用");
            return;
          }

          // 立即开始显示已缓存的答案内容
          const cachedContent = currentStreamingMessage.tempMessagesCache.join('');
          
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isReadyForAnswers: true,
              answerStarted: true,
              answerContent: cachedContent,
              answerStartTime: Date.now(),
              tempMessagesCache: [], // 清空临时缓存，后续Messages将实时添加
            },
          });

          // 立即更新UI显示已缓存的内容
          if (cachedContent) {
            console.log("立即显示已缓存的答案内容，长度:", cachedContent.length);
            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: cachedContent,
                      streamingContent: cachedContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });
          }

          console.log("答案处理已启动，后续Messages将实时添加");
        },

        addPendingMessage: (content: string) => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          console.log("添加Messages到队列:", {
            content: content.substring(0, 50) + "...",
            currentQueueLength: currentStreamingMessage.pendingMessages.length,
            isThinkingComplete: currentStreamingMessage.isThinkingComplete,
          });

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              pendingMessages: [
                ...currentStreamingMessage.pendingMessages,
                content,
              ],
            },
          }));
        },

        startProcessingMessages: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isProcessingMessages: true,
            },
          }));
        },

        completeMessageProcessing: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isProcessingMessages: false,
            },
          }));
        },

        completeCurrentMessage: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          console.log("完成当前Message，累积内容长度:", {
            beforeLength: currentStreamingMessage.answerContent.length,
            currentLength: currentStreamingMessage.currentMessageContent.length,
            pendingCount: currentStreamingMessage.pendingMessages.length,
          });

          // 将当前Message内容累积到总内容中
          const newAnswerContent =
            currentStreamingMessage.answerContent +
            currentStreamingMessage.currentMessageContent;

          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              answerContent: newAnswerContent,
              currentMessageContent: "",
              currentMessageComplete: true,
            },
          });
        },

        startNextMessage: () => {
          const { currentStreamingMessage } = get();

          console.log("startNextMessage被调用，当前状态:", {
            hasCurrentMessage: !!currentStreamingMessage,
            pendingCount: currentStreamingMessage?.pendingMessages.length || 0,
            isProcessingMessages: currentStreamingMessage?.isProcessingMessages,
          });

          if (
            !currentStreamingMessage ||
            currentStreamingMessage.pendingMessages.length === 0
          ) {
            console.log("队列处理完成，调用completeAnswerStream");
            get().completeAnswerStream();
            return;
          }

          const nextMessage = currentStreamingMessage.pendingMessages[0];
          console.log(
            "处理下一个Message:",
            nextMessage.substring(0, 50) + "..."
          );

          const remainingMessages =
            currentStreamingMessage.pendingMessages.slice(1);

          // 更新队列状态
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              pendingMessages: remainingMessages,
            },
          });

          // 处理新Message
          get().updateAnswerStream(nextMessage);
        },

        // 停止控制方法
        stopStreamingMessage: () => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          const now = Date.now();
          const updatedMessage = {
            ...currentStreamingMessage,
            isStopped: true,
            stopTime: now,
          };

          // 如果思考还在进行中，记录思考停止时间
          if (!currentStreamingMessage.isThinkingComplete) {
            updatedMessage.thinkingStopTime = now;
          }

          // 如果回答在进行中，记录回答停止时间
          if (
            currentStreamingMessage.answerStartTime &&
            !currentStreamingMessage.isAnswerComplete
          ) {
            updatedMessage.answerStopTime = now;
          }

          // 更新流式消息状态
          set({
            currentStreamingMessage: updatedMessage,
          });

          // 更新消息列表，标记为完成状态
          set({
            messages: messages.map((msg) =>
              msg.id === currentStreamingMessage.id
                ? {
                    ...msg,
                    content: currentStreamingMessage.answerContent,
                    isStreaming: false,
                    streamingContent: undefined,
                    thinkingProcess: msg.thinkingProcess
                      ? {
                          ...msg.thinkingProcess,
                          isStreaming: false,
                          isStopped: true,
                          // 保持原有的 isComplete 状态，不强制设置为 true
                        }
                      : undefined,
                  }
                : msg
            ),
          });

          // 清除流式状态
          setTimeout(() => {
            set({ currentStreamingMessage: undefined });
          }, 100);
        },

        startNewChat: () => {
          set({
            messages: [],
            currentChatId: "",
            isConversationStarted: false,
            inputValue: "",
            uploadResult: [],
          });
        },
        loadHistory: () => {},

        setChatHistory: async () => {
          //set({ chatHistory })
          try {
            console.log("🔄 Store: 开始获取历史数据...");
            const response = (await getHistory().send()) as HistoryApiResponse;
            console.log("📦 Store: 历史数据API响应:", response);
            
            if (response && Array.isArray(response.data)) {
              const chatHistory = response.data.map((item) => ({
                id: item.id,
                sessionTitle: item.sessionTitle,
                firstQuestion: item.firstQuestion,
                data: item.data,
                createTime: item.createTime,
              }));
              
              console.log("💾 Store: 更新历史数据状态:", {
                count: chatHistory.length,
                ids: chatHistory.map(chat => chat.id)
              });
              
              set({ chatHistory });
            } else {
              console.warn("⚠️ Store: 历史数据格式异常:", response);
            }
          } catch (error) {
            console.error("❌ Store: 获取历史数据失败:", error);
          }
        },

        loadChatSession: async (sessionId: string) => {
          set({
            currentChatId: sessionId,
            // messages: [],
            isLoading: true,
            isConversationStarted: true,
          });

          try {
            const response = (await getRecords({ sessionId }).send()) as {
              data: { question: string; answer: string }[];
            };
            const records = response.data;

            if (!Array.isArray(records)) {
              throw new Error("Invalid data format from getRecords");
            }

            const loadedMessages: Message[] = [];
            for (const record of records) {
              try {
                if (record.question) {
                  loadedMessages.push(JSON.parse(record.question));
                }
                if (record.answer) {
                  loadedMessages.push(JSON.parse(record.answer));
                }
              } catch (parseError) {
                console.error(
                  "Failed to parse message record:",
                  record,
                  parseError
                );
              }
            }

            loadedMessages.sort(
              (a, b) =>
                new Date(a.timestamp).getTime() -
                new Date(b.timestamp).getTime()
            );

            set({ messages: loadedMessages });
          } catch (error) {
            console.error("Failed to load chat session:", error);
          } finally {
            set({ isLoading: false });
          }
        },
      }),
      {
        name: "chat-storage",
        // 只持久化部分状态
        partialize: (state) => ({
          // chatHistory: state.chatHistory,
          sidebarCollapsed: state.sidebarCollapsed,
          userInfo: state.userInfo,
          isDeepMindEnabled: state.isDeepMindEnabled,
        }),
        version: 1, // Set the new version
        // migrate: (persistedState, version) => {
        //   const state = persistedState as any;
        //   if (version < 1 && state.chatHistory) {
        //     state.chatHistory = state.chatHistory.map((chat: any) => {
        //       if (chat.title && !chat.sessionTitle) {
        //         return {
        //           id: String(chat.id),
        //           sessionTitle: chat.title,
        //         };
        //       }
        //       return chat;
        //     });
        //   }
        //   return state;
        // },
      }
    ),
    {
      name: "chat-store",
    }
  )
);
