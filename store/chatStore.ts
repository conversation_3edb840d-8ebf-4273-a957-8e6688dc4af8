import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  getHistory,
  getRecords,
  saveRecord,
  type SessionRecord,
} from "@/services/chatService";

export interface CurrentFile {
  id: number;
  name: string;
  size: string;
  type: string;
  fileType: "PDF" | "DOC" | "DOCX" | "XLS" | "XLSX" | "PPT" | "PPTX" | "PNG" | "JPG" | "JPEG";
  attachmentText: string;
}

// 唯一ID生成器
let messageIdCounter = Date.now();
const generateUniqueId = () => {
  return ++messageIdCounter;
};

export interface Message {
  id: number;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  thinkingProcess?: {
    content: string;
    isStreaming?: boolean;
    isComplete?: boolean;
    isStopped?: boolean;
    duration?: number;
  };
  isStreaming?: boolean;
  streamingContent?: string;
}

export interface ChatHistory {
  id?: string;
  sessionTitle: string;
  firstQuestion?: string;
  data?: string;
  createBy?: string;
  createTime?: string;
  updateTime?: string;
  // 新增：后台生成状态
  isGenerating?: boolean;
  hasNewContent?: boolean;
}

export interface UserInfo {
  account?: string;
  deptId?: string;
  deptName?: string;
  email?: string;
  id?: number;
  avatar?: string;
  gender?: number;
  staffNumber?: string;
  realName?: string;
  nickName?: string;
  unionId?: string;
}

export interface HistoryApiResponseItem {
  id: string;
  sessionTitle: string;
  firstQuestion: string;
  data?: string;
  createTime: string;
}

export interface HistoryApiResponse {
  data: HistoryApiResponseItem[];
}

interface ChatState {
  // 聊天消息
  messages: Message[];

  // 聊天历史
  chatHistory: ChatHistory[];

  // 当前聊天ID
  currentChatId: string;

  // 输入状态
  inputValue: string;

  // 加载状态
  isLoading: boolean;

  // 侧边栏状态
  sidebarCollapsed: boolean;

  // 会话是否开始
  isConversationStarted: boolean;

  // 当前会话title
  currentTitle: string;

  // 深度思考开关状态
  isDeepMindEnabled: boolean;

  // 单个会话的流式数据状态
  currentStreamingMessage?: {
    id: number;
    thinkingContent: string;
    answerContent: string; // 累积的完整答案
    currentMessageContent: string; // 当前正在显示的单个Message内容
    isThinkingComplete: boolean;
    isThinkingStreamComplete: boolean; // 思考数据流是否完成（区别于打字机完成）
    isReadyForAnswers: boolean; // 是否准备好处理答案（思考过程打字机完成后）
    isAnswerComplete: boolean;
    answerStarted: boolean; // 标记答案是否已开始显示
    tempMessagesCache: string[]; // 暂存Messages数据，等待思考过程完成
    pendingMessages: string[]; // 缓存的Messages数据队列
    isProcessingMessages: boolean; // 是否正在处理Messages
    currentMessageComplete: boolean; // 当前Message的打字机是否完成
    isStopped: boolean; // 是否已停止
    startTime: number; // 整体开始时间
    stopTime?: number; // 整体停止时间
    thinkingStartTime?: number; // 思考开始时间
    thinkingStopTime?: number; // 思考停止时间
    answerStartTime?: number; // 回答开始时间
    answerStopTime?: number; // 回答停止时间
  };

  // 多会话流式状态管理
  sessionStreamingStates: Record<string, {
    streamingMessage?: {
      id: number;
      thinkingContent: string;
      answerContent: string;
      currentMessageContent: string;
      isThinkingComplete: boolean;
      isThinkingStreamComplete: boolean;
      isReadyForAnswers: boolean;
      isAnswerComplete: boolean;
      answerStarted: boolean;
      tempMessagesCache: string[];
      pendingMessages: string[];
      isProcessingMessages: boolean;
      currentMessageComplete: boolean;
      isStopped: boolean;
      startTime: number;
      stopTime?: number;
      thinkingStartTime?: number;
      thinkingStopTime?: number;
      answerStartTime?: number;
      answerStopTime?: number;
    };
    messages: Message[]; // 该会话的消息缓存
    abortController?: AbortController; // 该会话的请求控制器
  }>;

  // 当前活跃的后台生成会话
  backgroundGeneratingSessions: Set<string>;

  // 用户信息
  userInfo: UserInfo;

  // 文件上传状态
  uploadResult: CurrentFile[];

  // Actions
  setMessages: (messages: Message[]) => void;
  addMessage: (
    message: Omit<Message, "id" | "timestamp"> & { id?: number }
  ) => void;
  updateMessage: (id: number, updates: Partial<Message>) => void;
  deleteMessage: (id: number) => void;
  clearMessages: () => void;

  setChatHistory: () => void;

  setCurrentChatId: (id: string) => void;
  setInputValue: (value: string) => void;
  setIsLoading: (loading: boolean) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setIsConversationStarted: (started: boolean) => void;

  setCurrentTitle: (title: string) => void;

  // 深度思考状态操作
  setIsDeepMindEnabled: (enabled: boolean) => void;

  // 流式数据处理方法 - 支持多会话
  startStreamingMessage: (userMessage: string, sessionId?: string) => void;
  updateThinkingStream: (content: string, sessionId?: string) => void;
  completeThinkingStream: (sessionId?: string) => void;
  updateAnswerStream: (content: string, sessionId?: string) => void;
  completeAnswerStream: (sessionId?: string) => void;

  // Messages队列管理方法 - 支持多会话
  addTempMessage: (content: string, sessionId?: string) => void;
  startAnswerProcessing: (sessionId?: string) => void;
  addPendingMessage: (content: string, sessionId?: string) => void;
  startProcessingMessages: (sessionId?: string) => void;
  completeMessageProcessing: (sessionId?: string) => void;
  completeCurrentMessage: (sessionId?: string) => void;
  startNextMessage: (sessionId?: string) => void;

  // 停止控制方法 - 支持多会话
  stopStreamingMessage: (sessionId?: string) => void;

  // 多会话管理方法
  getSessionStreamingState: (sessionId: string) => any;
  setSessionStreamingState: (sessionId: string, state: any) => void;
  clearSessionStreamingState: (sessionId: string) => void;
  updateSessionGeneratingStatus: (sessionId: string, isGenerating: boolean) => void;
  markSessionHasNewContent: (sessionId: string) => void;
  clearSessionNewContentFlag: (sessionId: string) => void;

  startNewChat: () => void;

  loadHistory: () => void;

  loadChatSession: (sessionId: string) => Promise<void>;

  // 设置用户信息
  setUserInfo: (userInfo: UserInfo) => void;
  getUserInfo: () => UserInfo;

  setUploadResult: (files: CurrentFile[]) => void;
  addUploadFiles: (files: CurrentFile[]) => void;
  removeUploadFile: (id: number) => void;
  clearUploadResult: () => void;
}

export const useChatStore = create<ChatState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        messages: [],

        chatHistory: [],

        currentChatId: "",
        inputValue: "",
        isLoading: false,
        sidebarCollapsed: false,
        isConversationStarted: true,
        currentStreamingMessage: undefined,
        currentTitle: "",
        isDeepMindEnabled: false,
        userInfo: {},
        uploadResult: [],

        // 多会话状态初始化
        sessionStreamingStates: {},
        backgroundGeneratingSessions: new Set(),
        // Actions
        setUserInfo: (userInfo) => set({ userInfo }),
        getUserInfo: () => get().userInfo,

        setUploadResult: (files) => set({ uploadResult: files }),
        addUploadFiles: (files) => set((state) => ({ 
          uploadResult: [...state.uploadResult, ...files] 
        })),
        removeUploadFile: (id) => set((state) => ({ 
          uploadResult: state.uploadResult.filter((file) => file.id !== id) 
        })),
        clearUploadResult: () => set({ uploadResult: [] }),

        setMessages: (messages) => set({ messages }),

        setCurrentTitle: (currentTitle) => set({ currentTitle }),

        addMessage: (messageData) => {
          const newMessage: Message = {
            ...messageData,
            id: messageData.id || generateUniqueId(),
            timestamp: new Date(),
          };

          set((state) => ({
            messages: [...state.messages, newMessage],
            isConversationStarted: true,
          }));
        },

        updateMessage: (id, updates) => {
          set((state) => ({
            messages: state.messages.map((msg) =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          }));
        },

        deleteMessage: (id) => {
          set((state) => ({
            messages: state.messages.filter((msg) => msg.id !== id),
          }));
        },

        clearMessages: () => {
          set({
            messages: [],
            isConversationStarted: false,
          });
        },

        setCurrentChatId: (id) => set({ currentChatId: id }),
        setInputValue: (value) => set({ inputValue: value }),
        setIsLoading: (loading) => set({ isLoading: loading }),
        setSidebarCollapsed: (collapsed) =>
          set({ sidebarCollapsed: collapsed }),
        setIsConversationStarted: (started) =>
          set({ isConversationStarted: started }),

        // 深度思考状态操作
        setIsDeepMindEnabled: (enabled) => set({ isDeepMindEnabled: enabled }),

        // 流式数据处理方法 - 支持多会话
        startStreamingMessage: (userMessage: string, sessionId?: string) => {
          const { addMessage, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) {
            console.error("startStreamingMessage: 无法确定目标会话ID");
            return;
          }

          // 预生成唯一ID
          const userMessageId = generateUniqueId();
          const assistantMessageId = generateUniqueId();

          // 创建流式消息状态
          const now = Date.now();
          const streamingState = {
            id: assistantMessageId,
            thinkingContent: "",
            answerContent: "",
            currentMessageContent: "",
            isThinkingComplete: false,
            isThinkingStreamComplete: false,
            isReadyForAnswers: false,
            isAnswerComplete: false,
            answerStarted: false,
            tempMessagesCache: [],
            pendingMessages: [],
            isProcessingMessages: false,
            currentMessageComplete: false,
            isStopped: false,
            startTime: now,
            thinkingStartTime: now,
          };

          // 更新会话流式状态
          const updatedSessionStates = { ...sessionStreamingStates };
          if (!updatedSessionStates[targetSessionId]) {
            updatedSessionStates[targetSessionId] = { messages: [] };
          }
          updatedSessionStates[targetSessionId].streamingMessage = streamingState;

          // 如果是当前会话，设置为当前流式消息并添加到UI
          if (targetSessionId === currentChatId) {
            set({ currentStreamingMessage: streamingState });

            // 延迟添加消息到数组，给loading效果留出显示时间
            setTimeout(() => {
              // 添加用户消息，使用预设ID
              addMessage({
                id: userMessageId,
                type: "user",
                content: userMessage,
              });

              // 创建流式assistant消息，使用预设ID
              addMessage({
                id: assistantMessageId,
                type: "assistant",
                content: "",
                isStreaming: true,
                streamingContent: "",
                thinkingProcess: {
                  content: "",
                  isStreaming: true,
                  isComplete: false,
                },
              });
            }, 300); // 延迟300ms，给loading状态足够的显示时间
          } else {
            // 如果不是当前会话，将消息添加到会话缓存中
            const userMsg: Message = {
              id: userMessageId,
              type: "user",
              content: userMessage,
              timestamp: new Date(),
            };
            const assistantMsg: Message = {
              id: assistantMessageId,
              type: "assistant",
              content: "",
              timestamp: new Date(),
              isStreaming: true,
              streamingContent: "",
              thinkingProcess: {
                content: "",
                isStreaming: true,
                isComplete: false,
              },
            };
            updatedSessionStates[targetSessionId].messages.push(userMsg, assistantMsg);
          }

          set({
            sessionStreamingStates: updatedSessionStates,
            backgroundGeneratingSessions: new Set([...get().backgroundGeneratingSessions, targetSessionId])
          });

          // 更新历史会话的生成状态
          get().updateSessionGeneratingStatus(targetSessionId, true);
        },

        updateThinkingStream: (content: string, sessionId?: string) => {
          const { currentStreamingMessage, messages, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) return;

          // 获取目标会话的流式状态
          let targetStreamingMessage = currentStreamingMessage;
          if (sessionId && sessionId !== currentChatId) {
            const sessionState = sessionStreamingStates[sessionId];
            targetStreamingMessage = sessionState?.streamingMessage;
          }

          if (!targetStreamingMessage) return;

          // 累积思考内容
          const updatedThinkingContent = targetStreamingMessage.thinkingContent + content;
          const updatedStreamingMessage = {
            ...targetStreamingMessage,
            thinkingContent: updatedThinkingContent,
          };

          // 如果是当前会话，更新当前流式消息状态和UI
          if (targetSessionId === currentChatId) {
            set({ currentStreamingMessage: updatedStreamingMessage });

            // 更新消息列表中的thinking process
            set({
              messages: messages.map((msg) =>
                msg.id === targetStreamingMessage.id
                  ? {
                      ...msg,
                      thinkingProcess: {
                        content: updatedThinkingContent,
                        isStreaming: true,
                        isComplete: false,
                      },
                    }
                  : msg
              ),
            });
          } else {
            // 如果是后台会话，更新会话缓存
            const updatedSessionStates = { ...sessionStreamingStates };
            if (updatedSessionStates[targetSessionId]) {
              updatedSessionStates[targetSessionId].streamingMessage = updatedStreamingMessage;

              // 更新缓存消息中的thinking process
              updatedSessionStates[targetSessionId].messages =
                updatedSessionStates[targetSessionId].messages.map((msg) =>
                  msg.id === targetStreamingMessage.id
                    ? {
                        ...msg,
                        thinkingProcess: {
                          content: updatedThinkingContent,
                          isStreaming: true,
                          isComplete: false,
                        },
                      }
                    : msg
                );
            }
            set({ sessionStreamingStates: updatedSessionStates });
          }
        },

        completeThinkingStream: (sessionId?: string) => {
          const { currentStreamingMessage, messages, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) return;

          // 获取目标会话的流式状态
          let targetStreamingMessage = currentStreamingMessage;
          let targetMessages = messages;

          if (sessionId && sessionId !== currentChatId) {
            const sessionState = sessionStreamingStates[sessionId];
            targetStreamingMessage = sessionState?.streamingMessage;
            targetMessages = sessionState?.messages || [];
          }

          if (!targetStreamingMessage) return;

          console.log("完成思考过程，当前状态:", {
            sessionId: targetSessionId,
            thinkingContent: targetStreamingMessage.thinkingContent,
            thinkingContentLength: targetStreamingMessage.thinkingContent.length,
            tempMessagesCount: targetStreamingMessage.tempMessagesCache.length,
            isThinkingComplete: targetStreamingMessage.isThinkingComplete,
          });

          // 检查思考内容是否存在
          if (!targetStreamingMessage.thinkingContent) {
            console.log("没有思考内容，跳过思考过程完成");
            return;
          }

          const updatedStreamingMessage = {
            ...targetStreamingMessage,
            isThinkingComplete: true,
            isThinkingStreamComplete: true,
            thinkingStopTime: Date.now(),
          };

          // 更新消息中的thinking process为完成状态
          const updatedMessages = targetMessages.map((msg) =>
            msg.id === targetStreamingMessage.id
              ? {
                  ...msg,
                  thinkingProcess: {
                    content: targetStreamingMessage.thinkingContent,
                    isStreaming: false,
                    isComplete: true,
                  },
                }
              : msg
          );

          // 如果是当前会话，更新当前状态
          if (targetSessionId === currentChatId) {
            set({
              currentStreamingMessage: updatedStreamingMessage,
              messages: updatedMessages,
            });
          } else {
            // 如果是后台会话，更新会话缓存
            const updatedSessionStates = { ...sessionStreamingStates };
            if (updatedSessionStates[targetSessionId]) {
              updatedSessionStates[targetSessionId].streamingMessage = updatedStreamingMessage;
              updatedSessionStates[targetSessionId].messages = updatedMessages;
            }
            set({ sessionStreamingStates: updatedSessionStates });
          }

          console.log("思考过程状态更新完成，立即开始答案处理");

          // 立即启动答案处理，不等待打字机完成
          get().startAnswerProcessing(sessionId);
        },

        updateAnswerStream: (content: string) => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          if (currentStreamingMessage.isProcessingMessages) {
            console.log(
              "队列模式：处理Message内容:",
              content.substring(0, 50) + "..."
            );

            // 队列处理模式：直接累积内容，立即处理下一个
            const newAnswerContent =
              currentStreamingMessage.answerContent + content;

            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                answerContent: newAnswerContent,
                currentMessageContent: "", // 清空当前内容
                currentMessageComplete: true,
              },
            });

            // 更新消息显示
            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: newAnswerContent,
                      streamingContent: newAnswerContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });

            // 立即处理下一个Message
            console.log("队列模式：立即处理下一个Message");
            setTimeout(() => {
              get().startNextMessage();
            }, 0);
          } else {
            // 普通流式处理：累积内容
            const newContent = currentStreamingMessage.answerContent + content;

            set({
              currentStreamingMessage: {
                ...currentStreamingMessage,
                answerContent: newContent,
              },
            });

            set({
              messages: messages.map((msg) =>
                msg.id === currentStreamingMessage.id
                  ? {
                      ...msg,
                      content: newContent,
                      streamingContent: newContent,
                      isStreaming: true,
                    }
                  : msg
              ),
            });
          }
        },

        completeAnswerStream: async (sessionId?: string) => {
          const { currentStreamingMessage, messages, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) return;

          // 获取目标会话的流式状态和消息
          let targetStreamingMessage = currentStreamingMessage;
          let targetMessages = messages;

          if (sessionId && sessionId !== currentChatId) {
            const sessionState = sessionStreamingStates[sessionId];
            targetStreamingMessage = sessionState?.streamingMessage;
            targetMessages = sessionState?.messages || [];
          }

          if (!targetStreamingMessage) return;

          // Find the user message that triggered this response
          const userMessageIndex =
            targetMessages.findIndex((msg) => msg.id === targetStreamingMessage.id) - 1;
          const userMessage =
            userMessageIndex >= 0 ? targetMessages[userMessageIndex] : null;

          const assistantMessage = targetMessages.find(
            (msg) => msg.id === targetStreamingMessage.id
          );

          // Save the record to the backend
          try {
            if (
              userMessage &&
              userMessage.type === "user" &&
              assistantMessage
            ) {
              // Construct the final assistant message object
              const finalAssistantMessage: Message = {
                ...assistantMessage,
                content: targetStreamingMessage.answerContent,
                isStreaming: false,
                streamingContent: undefined,
                thinkingProcess: {
                  ...(assistantMessage.thinkingProcess || { content: "" }),
                  content: targetStreamingMessage.thinkingContent,
                  isStreaming: false,
                  isComplete: true,
                  isStopped: targetStreamingMessage.isStopped,
                  duration:
                    assistantMessage.thinkingProcess?.duration ||
                    targetStreamingMessage.thinkingStopTime,
                },
              };

              const recordData: SessionRecord = {
                sessionId: `${targetSessionId}`,
                question: JSON.stringify(userMessage),
                answer: JSON.stringify(finalAssistantMessage),
              };
              await saveRecord(recordData).send();
              console.log("Chat record saved successfully for session:", targetSessionId);
            }
          } catch (error) {
            console.error("Failed to save chat record:", error);
          }

          // 更新消息列表为完成状态
          const updatedMessages = targetMessages.map((msg) =>
            msg.id === targetStreamingMessage.id
              ? {
                  ...msg,
                  content: targetStreamingMessage.answerContent,
                  isStreaming: false,
                  streamingContent: undefined,
                  thinkingProcess: targetStreamingMessage.isStopped
                    ? msg.thinkingProcess // Preserve existing state on stop
                    : {
                        ...(msg.thinkingProcess || { content: "" }),
                        content: targetStreamingMessage.thinkingContent,
                        isStreaming: false,
                        isComplete: true,
                      },
                  isAnswerComplete: true,
                }
              : msg
          );

          // 如果是当前会话，更新当前状态
          if (targetSessionId === currentChatId) {
            set({
              currentStreamingMessage: {
                ...targetStreamingMessage,
                isAnswerComplete: true,
              },
              messages: updatedMessages,
            });

            // 清除当前流式消息状态
            setTimeout(() => {
              set({ currentStreamingMessage: undefined });
            }, 100);
          } else {
            // 如果是后台会话，更新会话缓存并标记有新内容
            const updatedSessionStates = { ...sessionStreamingStates };
            if (updatedSessionStates[targetSessionId]) {
              updatedSessionStates[targetSessionId].messages = updatedMessages;
              updatedSessionStates[targetSessionId].streamingMessage = {
                ...targetStreamingMessage,
                isAnswerComplete: true,
              };
            }
            set({ sessionStreamingStates: updatedSessionStates });

            // 标记会话有新内容
            get().markSessionHasNewContent(targetSessionId);
          }

          // 清除后台生成状态
          get().updateSessionGeneratingStatus(targetSessionId, false);

          console.log("Answer stream completed for session:", targetSessionId);
        },

        // Messages队列管理方法 - 支持多会话
        addTempMessage: (content: string, sessionId?: string) => {
          const { currentStreamingMessage, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) return;

          // 获取目标会话的流式状态
          let targetStreamingMessage = currentStreamingMessage;
          if (sessionId && sessionId !== currentChatId) {
            const sessionState = sessionStreamingStates[sessionId];
            targetStreamingMessage = sessionState?.streamingMessage;
          }

          if (!targetStreamingMessage) return;

          console.log("添加Messages数据:", {
            sessionId: targetSessionId,
            content: content.substring(0, 50) + "...",
            answerStarted: targetStreamingMessage.answerStarted,
            isReadyForAnswers: targetStreamingMessage.isReadyForAnswers,
          });

          // 如果答案已经开始显示，直接实时添加到答案内容
          if (targetStreamingMessage.answerStarted && targetStreamingMessage.isReadyForAnswers) {
            console.log("答案已开始，实时添加Messages内容");
            const newAnswerContent = targetStreamingMessage.answerContent + content;
            const updatedStreamingMessage = {
              ...targetStreamingMessage,
              answerContent: newAnswerContent,
            };

            // 如果是当前会话，更新当前流式消息状态和UI
            if (targetSessionId === currentChatId) {
              set({ currentStreamingMessage: updatedStreamingMessage });

              // 立即更新UI显示
              const { messages } = get();
              set({
                messages: messages.map((msg) =>
                  msg.id === targetStreamingMessage.id
                    ? {
                        ...msg,
                        content: newAnswerContent,
                        streamingContent: newAnswerContent,
                        isStreaming: true,
                      }
                    : msg
                ),
              });
            } else {
              // 如果是后台会话，更新会话缓存
              const updatedSessionStates = { ...sessionStreamingStates };
              if (updatedSessionStates[targetSessionId]) {
                updatedSessionStates[targetSessionId].streamingMessage = updatedStreamingMessage;

                // 更新缓存消息
                updatedSessionStates[targetSessionId].messages =
                  updatedSessionStates[targetSessionId].messages.map((msg) =>
                    msg.id === targetStreamingMessage.id
                      ? {
                          ...msg,
                          content: newAnswerContent,
                          streamingContent: newAnswerContent,
                          isStreaming: true,
                        }
                      : msg
                  );
              }
              set({ sessionStreamingStates: updatedSessionStates });
            }
          } else {
            // 答案尚未开始，暂存到缓存
            console.log("答案未开始，暂存到缓存");
            const updatedStreamingMessage = {
              ...targetStreamingMessage,
              tempMessagesCache: [
                ...targetStreamingMessage.tempMessagesCache,
                content,
              ],
            };

            if (targetSessionId === currentChatId) {
              set({ currentStreamingMessage: updatedStreamingMessage });
            } else {
              // 更新后台会话缓存
              const updatedSessionStates = { ...sessionStreamingStates };
              if (updatedSessionStates[targetSessionId]) {
                updatedSessionStates[targetSessionId].streamingMessage = updatedStreamingMessage;
              }
              set({ sessionStreamingStates: updatedSessionStates });
            }
          }
        },

        startAnswerProcessing: (sessionId?: string) => {
          const { currentStreamingMessage, messages, currentChatId, sessionStreamingStates } = get();
          const targetSessionId = sessionId || currentChatId;

          if (!targetSessionId) return;

          // 获取目标会话的流式状态
          let targetStreamingMessage = currentStreamingMessage;
          let targetMessages = messages;

          if (sessionId && sessionId !== currentChatId) {
            const sessionState = sessionStreamingStates[sessionId];
            targetStreamingMessage = sessionState?.streamingMessage;
            targetMessages = sessionState?.messages || [];
          }

          if (!targetStreamingMessage) {
            console.error("startAnswerProcessing: targetStreamingMessage不存在");
            return;
          }

          console.log("开始答案处理，立即显示已缓存的内容:", {
            sessionId: targetSessionId,
            cacheLength: targetStreamingMessage.tempMessagesCache.length,
            isThinkingComplete: targetStreamingMessage.isThinkingComplete,
            isReadyForAnswers: targetStreamingMessage.isReadyForAnswers,
          });

          // 检查是否已经在处理中
          if (targetStreamingMessage.isReadyForAnswers) {
            console.log("答案处理已经启动，跳过重复调用");
            return;
          }

          // 立即开始显示已缓存的答案内容
          const cachedContent = targetStreamingMessage.tempMessagesCache.join('');
          const updatedStreamingMessage = {
            ...targetStreamingMessage,
            isReadyForAnswers: true,
            answerStarted: true,
            answerContent: cachedContent,
            answerStartTime: Date.now(),
            tempMessagesCache: [], // 清空临时缓存，后续Messages将实时添加
          };

          // 更新消息显示已缓存的内容
          const updatedMessages = targetMessages.map((msg) =>
            msg.id === targetStreamingMessage.id
              ? {
                  ...msg,
                  content: cachedContent,
                  streamingContent: cachedContent,
                  isStreaming: true,
                }
              : msg
          );

          // 如果是当前会话，更新当前状态
          if (targetSessionId === currentChatId) {
            set({
              currentStreamingMessage: updatedStreamingMessage,
              messages: updatedMessages,
            });
          } else {
            // 如果是后台会话，更新会话缓存
            const updatedSessionStates = { ...sessionStreamingStates };
            if (updatedSessionStates[targetSessionId]) {
              updatedSessionStates[targetSessionId].streamingMessage = updatedStreamingMessage;
              updatedSessionStates[targetSessionId].messages = updatedMessages;
            }
            set({ sessionStreamingStates: updatedSessionStates });
          }

          if (cachedContent) {
            console.log("立即显示已缓存的答案内容，长度:", cachedContent.length);
          }

          console.log("答案处理已启动，后续Messages将实时添加");
        },

        addPendingMessage: (content: string) => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          console.log("添加Messages到队列:", {
            content: content.substring(0, 50) + "...",
            currentQueueLength: currentStreamingMessage.pendingMessages.length,
            isThinkingComplete: currentStreamingMessage.isThinkingComplete,
          });

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              pendingMessages: [
                ...currentStreamingMessage.pendingMessages,
                content,
              ],
            },
          }));
        },

        startProcessingMessages: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isProcessingMessages: true,
            },
          }));
        },

        completeMessageProcessing: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          set((state) => ({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              isProcessingMessages: false,
            },
          }));
        },

        completeCurrentMessage: () => {
          const { currentStreamingMessage } = get();
          if (!currentStreamingMessage) return;

          console.log("完成当前Message，累积内容长度:", {
            beforeLength: currentStreamingMessage.answerContent.length,
            currentLength: currentStreamingMessage.currentMessageContent.length,
            pendingCount: currentStreamingMessage.pendingMessages.length,
          });

          // 将当前Message内容累积到总内容中
          const newAnswerContent =
            currentStreamingMessage.answerContent +
            currentStreamingMessage.currentMessageContent;

          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              answerContent: newAnswerContent,
              currentMessageContent: "",
              currentMessageComplete: true,
            },
          });
        },

        startNextMessage: () => {
          const { currentStreamingMessage } = get();

          console.log("startNextMessage被调用，当前状态:", {
            hasCurrentMessage: !!currentStreamingMessage,
            pendingCount: currentStreamingMessage?.pendingMessages.length || 0,
            isProcessingMessages: currentStreamingMessage?.isProcessingMessages,
          });

          if (
            !currentStreamingMessage ||
            currentStreamingMessage.pendingMessages.length === 0
          ) {
            console.log("队列处理完成，调用completeAnswerStream");
            get().completeAnswerStream();
            return;
          }

          const nextMessage = currentStreamingMessage.pendingMessages[0];
          console.log(
            "处理下一个Message:",
            nextMessage.substring(0, 50) + "..."
          );

          const remainingMessages =
            currentStreamingMessage.pendingMessages.slice(1);

          // 更新队列状态
          set({
            currentStreamingMessage: {
              ...currentStreamingMessage,
              pendingMessages: remainingMessages,
            },
          });

          // 处理新Message
          get().updateAnswerStream(nextMessage);
        },

        // 停止控制方法
        stopStreamingMessage: () => {
          const { currentStreamingMessage, messages } = get();
          if (!currentStreamingMessage) return;

          const now = Date.now();
          const updatedMessage = {
            ...currentStreamingMessage,
            isStopped: true,
            stopTime: now,
          };

          // 如果思考还在进行中，记录思考停止时间
          if (!currentStreamingMessage.isThinkingComplete) {
            updatedMessage.thinkingStopTime = now;
          }

          // 如果回答在进行中，记录回答停止时间
          if (
            currentStreamingMessage.answerStartTime &&
            !currentStreamingMessage.isAnswerComplete
          ) {
            updatedMessage.answerStopTime = now;
          }

          // 更新流式消息状态
          set({
            currentStreamingMessage: updatedMessage,
          });

          // 更新消息列表，标记为完成状态
          set({
            messages: messages.map((msg) =>
              msg.id === currentStreamingMessage.id
                ? {
                    ...msg,
                    content: currentStreamingMessage.answerContent,
                    isStreaming: false,
                    streamingContent: undefined,
                    thinkingProcess: msg.thinkingProcess
                      ? {
                          ...msg.thinkingProcess,
                          isStreaming: false,
                          isStopped: true,
                          // 保持原有的 isComplete 状态，不强制设置为 true
                        }
                      : undefined,
                  }
                : msg
            ),
          });

          // 清除流式状态
          setTimeout(() => {
            set({ currentStreamingMessage: undefined });
          }, 100);
        },

        startNewChat: () => {
          set({
            messages: [],
            currentChatId: "",
            isConversationStarted: false,
            inputValue: "",
            uploadResult: [],
          });
        },
        loadHistory: () => {},

        setChatHistory: async () => {
          //set({ chatHistory })
          try {
            console.log("🔄 Store: 开始获取历史数据...");
            const response = (await getHistory().send()) as HistoryApiResponse;
            console.log("📦 Store: 历史数据API响应:", response);
            
            if (response && Array.isArray(response.data)) {
              const chatHistory = response.data.map((item) => ({
                id: item.id,
                sessionTitle: item.sessionTitle,
                firstQuestion: item.firstQuestion,
                data: item.data,
                createTime: item.createTime,
              }));
              
              console.log("💾 Store: 更新历史数据状态:", {
                count: chatHistory.length,
                ids: chatHistory.map(chat => chat.id)
              });
              
              set({ chatHistory });
            } else {
              console.warn("⚠️ Store: 历史数据格式异常:", response);
            }
          } catch (error) {
            console.error("❌ Store: 获取历史数据失败:", error);
          }
        },

        loadChatSession: async (sessionId: string) => {
          const { sessionStreamingStates, currentStreamingMessage } = get();

          // 保存当前会话的流式状态（如果有的话）
          const currentChatId = get().currentChatId;
          if (currentChatId && currentStreamingMessage) {
            const updatedSessionStates = { ...sessionStreamingStates };
            if (!updatedSessionStates[currentChatId]) {
              updatedSessionStates[currentChatId] = { messages: [] };
            }
            updatedSessionStates[currentChatId].streamingMessage = currentStreamingMessage;
            updatedSessionStates[currentChatId].messages = get().messages;
            set({ sessionStreamingStates: updatedSessionStates });
          }

          set({
            currentChatId: sessionId,
            isLoading: true,
            isConversationStarted: true,
          });

          try {
            // 检查是否有缓存的会话状态
            const sessionState = sessionStreamingStates[sessionId];
            if (sessionState && sessionState.messages.length > 0) {
              // 使用缓存的消息
              set({
                messages: sessionState.messages,
                currentStreamingMessage: sessionState.streamingMessage
              });

              // 清除新内容标记
              get().clearSessionNewContentFlag(sessionId);
            } else {
              // 从服务器加载历史消息
              const response = (await getRecords({ sessionId }).send()) as {
                data: { question: string; answer: string }[];
              };
              const records = response.data;

              if (!Array.isArray(records)) {
                throw new Error("Invalid data format from getRecords");
              }

              const loadedMessages: Message[] = [];
              for (const record of records) {
                try {
                  if (record.question) {
                    loadedMessages.push(JSON.parse(record.question));
                  }
                  if (record.answer) {
                    loadedMessages.push(JSON.parse(record.answer));
                  }
                } catch (parseError) {
                  console.error(
                    "Failed to parse message record:",
                    record,
                    parseError
                  );
                }
              }

              loadedMessages.sort(
                (a, b) =>
                  new Date(a.timestamp).getTime() -
                  new Date(b.timestamp).getTime()
              );

              set({
                messages: loadedMessages,
                currentStreamingMessage: undefined
              });
            }
          } catch (error) {
            console.error("Failed to load chat session:", error);
          } finally {
            set({ isLoading: false });
          }
        },

        // 多会话管理方法
        getSessionStreamingState: (sessionId: string) => {
          const { sessionStreamingStates } = get();
          return sessionStreamingStates[sessionId];
        },

        setSessionStreamingState: (sessionId: string, state: any) => {
          const { sessionStreamingStates } = get();
          set({
            sessionStreamingStates: {
              ...sessionStreamingStates,
              [sessionId]: state,
            },
          });
        },

        clearSessionStreamingState: (sessionId: string) => {
          const { sessionStreamingStates, backgroundGeneratingSessions } = get();
          const updatedStates = { ...sessionStreamingStates };
          delete updatedStates[sessionId];

          const updatedGeneratingSessions = new Set(backgroundGeneratingSessions);
          updatedGeneratingSessions.delete(sessionId);

          set({
            sessionStreamingStates: updatedStates,
            backgroundGeneratingSessions: updatedGeneratingSessions,
          });
        },

        updateSessionGeneratingStatus: (sessionId: string, isGenerating: boolean) => {
          const { chatHistory } = get();
          const updatedHistory = chatHistory.map(chat =>
            chat.id === sessionId
              ? { ...chat, isGenerating }
              : chat
          );
          set({ chatHistory: updatedHistory });
        },

        markSessionHasNewContent: (sessionId: string) => {
          const { chatHistory } = get();
          const updatedHistory = chatHistory.map(chat =>
            chat.id === sessionId
              ? { ...chat, hasNewContent: true, isGenerating: false }
              : chat
          );
          set({ chatHistory: updatedHistory });
        },

        clearSessionNewContentFlag: (sessionId: string) => {
          const { chatHistory } = get();
          const updatedHistory = chatHistory.map(chat =>
            chat.id === sessionId
              ? { ...chat, hasNewContent: false }
              : chat
          );
          set({ chatHistory: updatedHistory });
        },
      }),
      {
        name: "chat-storage",
        // 只持久化部分状态
        partialize: (state) => ({
          // chatHistory: state.chatHistory,
          sidebarCollapsed: state.sidebarCollapsed,
          userInfo: state.userInfo,
          isDeepMindEnabled: state.isDeepMindEnabled,
        }),
        version: 1, // Set the new version
        // migrate: (persistedState, version) => {
        //   const state = persistedState as any;
        //   if (version < 1 && state.chatHistory) {
        //     state.chatHistory = state.chatHistory.map((chat: any) => {
        //       if (chat.title && !chat.sessionTitle) {
        //         return {
        //           id: String(chat.id),
        //           sessionTitle: chat.title,
        //         };
        //       }
        //       return chat;
        //     });
        //   }
        //   return state;
        // },
      }
    ),
    {
      name: "chat-store",
    }
  )
);
