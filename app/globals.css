@import "tailwindcss";

@theme {
  /* 自定义字体 */
  --font-family: PingFang SC; 

  /* 自定义断点 */
  --breakpoint-3xl: 1920px;

  /* 自定义颜色 */
  --color-brand-50: oklch(0.98 0.02 250);
  --color-brand-100: oklch(0.95 0.05 250);
  --color-brand-500: oklch(0.65 0.15 250);
  --color-brand-900: oklch(0.25 0.15 250);

  /* 自定义动画缓动 */
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 自定义基础样式 */
body {
  font-family: var(--font-sans);
  font-feature-settings: "cv03", "cv04", "cv11";
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.custom-textarea {
        min-height: 4rem;
        max-height: 19rem;
        overflow-y: hidden;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 transparent;
        box-sizing: border-box;
        padding-left: 1.5rem;
        padding-right: 4.5rem;
        padding-top: 0.75rem;
        padding-bottom: 1rem;
      }
      
      .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(1rem);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .custom-scrollbar::-webkit-scrollbar {
        width: 0.5rem;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 0.25rem;
        margin: 2rem 0.75rem 1rem 0.75rem;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 0.25rem;
        border: 1px solid transparent;
        background-clip: padding-box;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
        background-clip: padding-box;
      }
      .custom-scrollbar::-webkit-scrollbar-corner {
        background: transparent;
      }
      .custom-scrollbar:focus {
        outline: none;
      }

      /* History session scrollbar styles */
      .history-scroll-container {
        scrollbar-width: thin;
        scrollbar-color: transparent transparent;
      }

      .history-scroll-container.show-scrollbar {
        scrollbar-color: rgba(203, 213, 225, 0.8) transparent;
      }

      .history-scroll-container::-webkit-scrollbar {
        width: 4px;
      }

      .history-scroll-container::-webkit-scrollbar-track {
        background: transparent;
      }

      .history-scroll-container::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 2px;
        transition: background 0.2s ease;
      }

      .history-scroll-container.show-scrollbar::-webkit-scrollbar-thumb {
        background: rgba(203, 213, 225, 0.8);
      }

      .history-scroll-container.show-scrollbar::-webkit-scrollbar-thumb:hover {
        background: rgba(148, 163, 184, 0.9);
      }