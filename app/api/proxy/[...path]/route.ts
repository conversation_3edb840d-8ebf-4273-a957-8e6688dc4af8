import { NextRequest } from 'next/server';
import { handleProxyRequest, createOptionsResponse, PROXY_CONFIGS } from '@/lib/proxyUtils';

interface RouteParams {
  params: Promise<{ path: string[] }>;
}

export async function GET(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('GET', request, params.path, PROXY_CONFIGS.system);
}

export async function POST(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('POST', request, params.path, PROXY_CONFIGS.system);
}

export async function PUT(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('PUT', request, params.path, PROXY_CONFIGS.system);
}

export async function DELETE(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('DELETE', request, params.path, PROXY_CONFIGS.system);
}

export async function PATCH(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('PATCH', request, params.path, PROXY_CONFIGS.system);
}

export async function OPTIONS() {
  return createOptionsResponse(PROXY_CONFIGS.system.serviceName);
} 