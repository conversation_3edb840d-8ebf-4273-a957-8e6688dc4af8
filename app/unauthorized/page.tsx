"use client";

import LottieAnimation from "@/components/LottieAnimation";
import animationData from "./unauthorized.json";

const UnauthorizedPage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-4 text-center">
        <LottieAnimation
          animationData={animationData}
          className="w-64 h-64 mx-auto"
        />
        <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-purple-700 bg-clip-text text-transparent">
        访问被拒绝
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
        对不起，您无权访问此系统。
        </p>
        <p className="text-gray-600 dark:text-gray-300">
        如果您认为这是错误，请联系管理员。
        </p>
        {/* <button
          className="px-6 py-2 mt-4 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Back to Login
        </button> */}
      </div>
    </div>
  );
};

export default UnauthorizedPage; 