import { createAlova } from "alova";
import adapterFetch from "alova/fetch";
import ReactHook from "alova/react";
import { logout } from "./auth";

// AlovaServiceConfig 接口类型
export interface AlovaServiceConfig {
  serviceType: "chat" | "system";
  baseURL?: string;
  timeout?: number;
  clientType?: string;
}

// 预设配置常量
export const CHAT_CONFIG: AlovaServiceConfig = {
  serviceType: "chat",
  baseURL: "/api/chat-proxy",
  timeout: 60000,
  clientType: "web-chat",
};

export const SYSTEM_CONFIG: AlovaServiceConfig = {
  serviceType: "system",
  baseURL: "/api/proxy",
  timeout: 30000,
};

// createAlovaInstance 工厂函数
export function createAlovaInstance(config: AlovaServiceConfig) {
  const isChat = config.serviceType === "chat";
  const servicePrefix = isChat ? "Chat " : "";

  return createAlova({
    // 基础 URL
    baseURL: config.baseURL,

    // 请求适配器配置
    requestAdapter: adapterFetch(),

    // React Hook 适配器
    statesHook: ReactHook,

    // 请求超时设置
    timeout: config.timeout,

    // 全局请求拦截器
    beforeRequest(method) {
      // 添加认证 token
      if (typeof window !== "undefined") {
        const token = localStorage.getItem("auth_token");
        if (token) {
          method.config.headers = {
            ...method.config.headers,
            Authorization: `${token}`,
          };
        }
      }

      // 智能合并headers，优先保留方法级别的关键设置
      const existingHeaders = method.config.headers || {};
      const hasContentType =
        existingHeaders["Content-Type"] || existingHeaders["content-type"];

      // 检查是否为 FormData 请求
      const isFormData = method.data instanceof FormData;

      const commonHeaders: Record<string, string> = {
        Accept: "application/json",
        "X-Requested-With": "XMLHttpRequest",
        "X-Frontend-Origin":
          typeof window !== "undefined"
            ? window.location.origin
            : "http://localhost:3000",
        "X-API-Version": "1.0",
      };

      // 只有在方法级别未设置Content-Type且不是FormData时才添加默认Content-Type
      if (!hasContentType && !isFormData) {
        commonHeaders["Content-Type"] = "application/json";
      }

      // 添加特定的clientType头（仅chat服务）
      if (config.clientType) {
        commonHeaders["X-Client-Type"] = config.clientType;
      }

      method.config.headers = {
        ...commonHeaders,
        ...existingHeaders,
      };

      console.log(`🚀 ${servicePrefix}Alova代理请求:`, {
        method: method.type,
        url: method.url,
        headers: method.config.headers,
        isFormData, // 添加这个日志帮助调试
      });
    },

    // 全局响应拦截器
    responded: {
      // 成功响应拦截器
      onSuccess: async (response: Response) => {
        console.log(`📦 ${servicePrefix}Alova代理响应:`, {
          status: response.status,
          statusText: response.statusText,
          url: response.url,
        });

        const text = await response.text();

        // 处理HTTP错误状态
        if (response.status >= 400) {
          let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

          try {
            const errorData = JSON.parse(text);
            errorMessage = errorData.message || errorData.error || errorMessage;
          } catch {
            // 如果解析失败，使用默认错误消息
          }

          throw new Error(errorMessage);
        }

        const contentType = response.headers.get("content-type");
        const isJson = contentType && contentType.includes("application/json");

        if (isJson) {
          try {
            const data = JSON.parse(text);

            if (data.code === 409) {
              if (typeof window !== "undefined") {
                window.location.href = "/unauthorized";
              }
              // 中断Alova调用链
              return new Promise(() => {});
            }

            if (data.code === 401 || data.code === 408) {
              logout();
              // 中断Alova调用链
              return new Promise(() => {});
            }

            return data;
          } catch (error) {
            console.warn(`⚠️ 解析${servicePrefix}响应失败:`, error);
            // 即使是JSON类型，解析也可能失败
            return text || {};
          }
        } else {
          // 非JSON响应直接返回文本
          return text || {};
        }
      },

      // 错误响应拦截器
      onError: (err: Error) => {
        console.error(`❌ ${servicePrefix}Alova请求错误:`, err);

        // 检查是否是网络相关错误
        if (
          err.message.includes("NetworkError") ||
          err.message.includes("Failed to fetch") ||
          err.name === "TypeError"
        ) {
          console.error(`🌐 ${servicePrefix}网络错误:`, {
            message: err.message,
            name: err.name,
          });

          const networkErrorMsg = isChat
            ? "Chat服务连接失败，请检查网络连接或稍后重试。"
            : "网络连接失败，请检查网络连接或稍后重试。";

          throw new Error(networkErrorMsg);
        }

        // 处理超时错误
        if (err.message.includes("timeout") || err.name === "AbortError") {
          const timeoutErrorMsg = isChat
            ? "Chat请求超时，请检查网络连接或稍后重试。"
            : "请求超时，请检查网络连接或稍后重试。";

          throw new Error(timeoutErrorMsg);
        }

        // 其他错误直接抛出
        throw err;
      },
    },
  });
}

// 创建预配置实例
export const chatAlovaInstance = createAlovaInstance(CHAT_CONFIG);
export const alovaInstance = createAlovaInstance(SYSTEM_CONFIG);

// 通用CRUD方法工厂函数
function createCRUDMethods(instance: ReturnType<typeof createAlovaInstance>) {
  return {
    GET: (url: string, config?: Record<string, unknown>) => {
      return instance.Get(url, config);
    },
    POST: (
      url: string,
      data?: Record<string, unknown>,
      config?: Record<string, unknown>
    ) => {
      return instance.Post(url, data, config);
    },
    PUT: (
      url: string,
      data?: Record<string, unknown>,
      config?: Record<string, unknown>
    ) => {
      return instance.Put(url, data, config);
    },
    DELETE: (url: string, config?: Record<string, unknown>) => {
      return instance.Delete(url, config);
    },
  };
}

// Chat专用CRUD方法
const chatMethods = createCRUDMethods(chatAlovaInstance);
export const CHAT_GET = chatMethods.GET;
export const CHAT_POST = chatMethods.POST;
export const CHAT_PUT = chatMethods.PUT;
export const CHAT_DELETE = chatMethods.DELETE;

// System专用CRUD方法
const systemMethods = createCRUDMethods(alovaInstance);
export const GET = systemMethods.GET;
export const POST = systemMethods.POST;
export const PUT = systemMethods.PUT;
export const DELETE = systemMethods.DELETE;
