import { NextRequest, NextResponse } from 'next/server';

export interface ProxyConfig {
  baseUrl: string;
  serviceName: string;
  userAgent: string;
  timeout?: number;
}

// 检测是否为流式请求
function isStreamRequest(request: NextRequest): boolean {
  const acceptHeader = request.headers.get('Accept');
  return acceptHeader?.includes('text/event-stream') || false;
}

// 处理流式响应
async function handleStreamResponse(response: Response, config: ProxyConfig): Promise<NextResponse> {
  console.log(`🌊 处理${config.serviceName}流式响应`);
  
  // 创建流式CORS头部
  const streamHeaders = createStreamCorsHeaders(response.headers.get('content-type') || 'text/event-stream');
  
  // 转发重要的响应头
  const importantHeaders = ['Set-Cookie', 'Cache-Control', 'ETag'];
  importantHeaders.forEach(headerName => {
    const value = response.headers.get(headerName);
    if (value) {
      streamHeaders.set(headerName, value);
    }
  });

  // 直接转发流式响应体，不进行缓冲
  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: streamHeaders,
  });
}

// 为流式响应创建CORS头部，不强制设置Content-Type
export function createStreamCorsHeaders(contentType?: string): Headers {
  const headers = new Headers({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-API-Version, Cookie, X-Client-Type, X-Frontend-Origin',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
  });
  
  if (contentType) {
    headers.set('Content-Type', contentType);
  }
  
  return headers;
}

export async function handleProxyRequest(
  method: string,
  request: NextRequest,
  pathSegments: string[],
  config: ProxyConfig
) {
  try {
    // 检测是否为流式请求
    const isStream = isStreamRequest(request);
    
    // 构建目标URL
    const path = pathSegments.join('/');
    const searchParams = request.nextUrl.searchParams.toString();
    const targetUrl = `${config.baseUrl}/${path}${searchParams ? `?${searchParams}` : ''}`;

    console.log(`🔄 ${config.serviceName}代理${method}请求:`, targetUrl, isStream ? '(流式)' : '(非流式)');

    // 准备请求头
    const headers: Record<string, string> = {
      'User-Agent': config.userAgent,
    };

    // 对于非流式请求，设置默认的Content-Type和Accept
    if (!isStream) {
      headers['Content-Type'] = 'application/json';
      headers['Accept'] = 'application/json';
    }

    // 转发原始Accept头（对流式请求很重要）
    const acceptHeader = request.headers.get('Accept');
    if (acceptHeader) {
      headers.Accept = acceptHeader;
    }

    // 转发原始Content-Type头
    const contentTypeHeader = request.headers.get('Content-Type');
    if (contentTypeHeader) {
      headers['Content-Type'] = contentTypeHeader;
    }

    // 转发认证头
    const authHeader = request.headers.get('Authorization');
    if (authHeader) {
      headers.Authorization = authHeader;
    }

    // 转发其他重要头部
    const xRequestedWith = request.headers.get('X-Requested-With');
    if (xRequestedWith) {
      headers['X-Requested-With'] = xRequestedWith;
    }

    // 转发Cookie
    const cookieHeader = request.headers.get('Cookie');
    if (cookieHeader) {
      headers.Cookie = cookieHeader;
    }

    // 转发其他自定义头部
    const customHeaders = ['X-API-Version', 'X-Client-Type', 'X-Frontend-Origin'];
    customHeaders.forEach(headerName => {
      const value = request.headers.get(headerName);
      if (value) {
        headers[headerName] = value;
      }
    });

    // 准备请求配置
    const requestConfig: RequestInit = {
      method,
      headers,
      // 设置超时
      signal: config.timeout ? AbortSignal.timeout(config.timeout) : undefined,
    };

    // 对于有body的请求，添加请求体
    if (method !== 'GET' && method !== 'DELETE' && method !== 'HEAD') {
      try {
        const body = await request.text();
        if (body) {
          requestConfig.body = body;
        }
      } catch (error) {
        console.warn(`⚠️ 读取${config.serviceName}请求体失败:`, error);
      }
    }

    // 发送请求到后端API
    console.log(`📤 发送${config.serviceName}请求配置:`, {
      method,
      url: targetUrl,
      headers,
      hasBody: !!requestConfig.body,
      isStream,
    });

    const response = await fetch(targetUrl, requestConfig);
    
    console.log(`📥 ${config.serviceName}后端响应:`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      isStream,
    });

    // 检查响应是否为流式响应
    const responseContentType = response.headers.get('content-type');
    const isStreamResponse = responseContentType?.includes('text/event-stream') || 
                           responseContentType?.includes('text/stream') || 
                           isStream;

    // 如果是流式响应，直接转发
    if (isStreamResponse) {
      console.log(`🌊 检测到${config.serviceName}流式响应，直接转发`);
      return await handleStreamResponse(response, config);
    }

    // 非流式响应的原有处理逻辑
    let responseData;
    
    try {
      if (responseContentType && responseContentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        const text = await response.text();
        // 尝试解析为JSON
        try {
          responseData = JSON.parse(text);
        } catch {
          responseData = text;
        }
      }
    } catch (error) {
      console.warn(`⚠️ 解析${config.serviceName}响应数据失败:`, error);
      responseData = {};
    }

    // 构建响应头，包含完整的CORS支持
    const responseHeaders = createCorsHeaders();

    // 转发一些重要的响应头
    const importantHeaders = ['Set-Cookie', 'Cache-Control', 'ETag'];
    importantHeaders.forEach(headerName => {
      const value = response.headers.get(headerName);
      if (value) {
        responseHeaders.set(headerName, value);
      }
    });

    console.log(`✅ ${config.serviceName}代理响应成功:`, response.status);

    return new NextResponse(JSON.stringify(responseData), {
      status: response.status,
      headers: responseHeaders,
    });

  } catch (error) {
    console.error(`❌ ${config.serviceName}代理请求失败:`, error);
    
    // 检查是否为流式请求的错误处理
    const isStream = isStreamRequest(request);
    
    const errorResponse = {
      error: `${config.serviceName}代理请求失败`,
      message: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
      service: config.serviceName.toLowerCase().replace(/\s+/g, '-'),
      requestType: isStream ? 'stream' : 'standard',
    };

    // 对流式请求错误使用流式CORS头部
    const corsHeaders = isStream ? createStreamCorsHeaders('application/json') : createCorsHeaders();

    return new NextResponse(JSON.stringify(errorResponse), {
      status: 500,
      headers: corsHeaders,
    });
  }
}

export function createCorsHeaders(): Headers {
  return new Headers({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-API-Version, Cookie, X-Client-Type, X-Frontend-Origin',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
    'Content-Type': 'application/json',
  });
}

export function createOptionsResponse(serviceName: string): NextResponse {
  console.log(`🔍 处理${serviceName} CORS预检请求`);
  
  return new NextResponse(null, {
    status: 200,
    headers: createCorsHeaders(),
  });
}

// 预定义的代理配置
export const PROXY_CONFIGS = {
  system: {
    baseUrl: process.env.BACKEND_API_URL || 'http://10.180.40.79/api',
    serviceName: '系统',
    userAgent: 'HR-System-Proxy/1.0',
    timeout: 30000,
  },
  chat: {
    baseUrl: process.env.CHAT_API_URL || 'http://10.31.141.251:8306',
    serviceName: 'Chat',
    userAgent: 'HR-Chat-Proxy/1.0',
    timeout: 60000,
  },
} as const; 