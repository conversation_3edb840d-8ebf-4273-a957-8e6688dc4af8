import { login } from "@/services/chatService";

import { useChatStore, UserInfo } from "@/store/chatStore";

// Token 存储的 key
const TOKEN_KEY = "auth_token";

const { setUserInfo } = useChatStore.getState();

interface LoginResponse {
  data: {
    tokenValue: string;
  };
}

// 获取回调 URL
export const getCallbackUrl = (): string => {
  return process.env.NEXT_PUBLIC_CALLBACK_URL || "http://localhost:3000/login";
};

// 从 URL 中提取 code 参数
export const getCodeFromUrl = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get("code");
  } catch (error) {
    console.error("Failed to get code from URL:", error);
    return null;
  }
};

// 清除 URL 中的 code 参数
export const removeCodeFromUrl = (): void => {
  if (typeof window === "undefined") return;

  try {
    const url = new URL(window.location.href);
    url.searchParams.delete("code");

    // 使用 history.replaceState 清除 URL 参数，但不刷新页面
    window.history.replaceState({}, "", url.toString());
  } catch (error) {
    console.error("Failed to remove code from URL:", error);
  }
};

// 获取 token
export const getToken = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    return localStorage.getItem(TOKEN_KEY);
  } catch (error) {
    console.error("Failed to get token:", error);
    return null;
  }
};

// 设置 token
export const setToken = (token: string): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(TOKEN_KEY, token);
    console.log("Token saved successfully:", token);
  } catch (error) {
    console.error("Failed to set token:", error);
  }
};

// 清除 token
export const removeToken = (): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.removeItem(TOKEN_KEY);
    console.log("Token removed successfully");
  } catch (error) {
    console.error("Failed to remove token:", error);
  }
};

// 处理认证流程：检查 URL 中的 code 或现有的 token
export const handleAuthFlow = async (): Promise<boolean> => {
  if (typeof window === "undefined") return false;

  try {
    // 首先检查 URL 中是否有 code 参数
    const codeFromUrl = getCodeFromUrl();

    if (codeFromUrl) {
      console.log("Found code in URL:", codeFromUrl);

      try {
        // 使用 code 调用登录接口
        const { data: loginResponse } = (await login({
          code: codeFromUrl,
          redirectUrl: window.location.origin,
        })) as LoginResponse;
        // 如果登录成功，保存返回的 token
        if (loginResponse && loginResponse.tokenValue) {
          setToken(loginResponse.tokenValue);
        } else {
          // 如果没有返回 token，直接使用 code 作为 token
          setToken(codeFromUrl);
        }
        setUserInfo(loginResponse as UserInfo);

        // 清除 URL 中的 code 参数
        removeCodeFromUrl();

        return true;
      } catch (error) {
        console.error("Login API call failed:", error);

        // 如果 API 调用失败，仍然可以选择使用 code 作为 token
        setToken(codeFromUrl);
        removeCodeFromUrl();

        return true;
      }
    }

    // 如果没有 code，检查是否已有 token
    const existingToken = getToken();

    if (existingToken && isTokenValid(existingToken)) {
      console.log("Found valid existing token");
      return true;
    }

    window.location.href = process.env.NEXT_PUBLIC_CALLBACK_URL!;
    return false;
  } catch (error) {
    console.error("Auth flow error:", error);
    return false;
  }
};

// 检查是否已认证
export const isAuthenticated = (): boolean => {
  const token = getToken();
  return token !== null && isTokenValid(token);
};

// 验证 token 是否有效
export const isTokenValid = (token: string): boolean => {
  try {
    // 简单检查：token 不为空且长度合理
    if (!token || token.length < 10) {
      return false;
    }

    // 如果是 JWT token，解析并检查过期时间
    if (token.includes(".")) {
      try {
        const parts = token.split(".");
        if (parts.length !== 3) return true; // 不是标准 JWT，但当作有效

        const payload = JSON.parse(atob(parts[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        // 检查是否过期
        if (payload.exp && payload.exp < currentTime) {
          return false;
        }
      } catch (e) {
        // JWT 解析失败，但不一定是无效 token
        console.warn("Token parsing failed, but treating as valid:", e);
      }
    }

    return true;
  } catch (error) {
    console.error("Token validation error:", error);
    return false;
  }
};

// 登出
export const logout = (): void => {
  removeToken();

  // 跳转到登录页
  const callbackUrl = getCallbackUrl();
  console.log("Logging out, redirecting to:", callbackUrl);
  window.location.href = callbackUrl;
};

// 跳转到登录页
export const redirectToLogin = (): void => {
  const callbackUrl = getCallbackUrl();
  window.location.href = callbackUrl;
};

// 解析 JWT token（如果是 JWT 格式）
export const parseJwtToken = (
  token: string
): Record<string, unknown> | null => {
  try {
    if (!token.includes(".")) return null;

    const parts = token.split(".");
    if (parts.length !== 3) return null;

    const payload = JSON.parse(atob(parts[1]));
    return payload as Record<string, unknown>;
  } catch (error) {
    console.error("Failed to parse JWT token:", error);
    return null;
  }
};

// 获取用户信息（从 token 中解析）
export const getUserFromToken = (): Record<string, unknown> | null => {
  const token = getToken();
  if (!token) return null;

  const payload = parseJwtToken(token);
  if (!payload) return null;

  return (payload.user as Record<string, unknown>) || payload;
};
