# HR Frontend - AI点将

这是一个基于 Next.js 构建的智能人才推荐系统前端应用，集成了AI聊天功能和现代化的用户界面。

## 功能特性

- 🤖 AI智能聊天界面，支持Markdown渲染
- 🔐 完整的用户认证系统（支持OAuth回调）
- 💾 对话历史持久化存储
- 🎨 现代化UI设计，响应式布局
- 📡 完整的HTTP请求管理和状态管理
- 🔧 TypeScript全栈类型安全

## 环境变量配置

创建 `.env.local` 文件并配置以下环境变量：

```bash
# 后端API基础URL
BACKEND_API_URL=http://************/api

# 登录回调URL
NEXT_PUBLIC_CALLBACK_URL=http://localhost:3000/login
```

## 开发设置

首先安装依赖：

```bash
npm install
# 或
pnpm install
```

然后启动开发服务器：

```bash
npm run dev
# 或
pnpm dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## API代理

为了解决CORS跨域问题，本项目使用了Next.js API路由作为代理：

- 前端请求：`/api/proxy/*`
- 代理到：`${BACKEND_API_URL}/*`

所有的API请求都会通过代理转发到后端服务器，并自动添加CORS头。

## 技术栈

- **框架**: Next.js 15.3.3
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand + Alova.js
- **UI组件**: Shadcn UI, Radix UI
- **Markdown**: react-markdown + rehype-highlight
- **认证**: localStorage + JWT

## 项目结构

```
├── app/                    # Next.js App Router
│   ├── api/proxy/         # API代理路由
│   ├── login/             # 登录页面
│   └── page.tsx           # 主页
├── components/            # React组件
├── lib/                   # 工具函数
├── services/              # API服务
├── store/                 # Zustand状态管理
└── public/                # 静态资源
```

