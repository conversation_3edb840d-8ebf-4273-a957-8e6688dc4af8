# 代理架构文档

## 📋 概览

本项目使用双代理架构来解决CORS问题，将前端请求路由到不同的后端服务。通过共享工具函数消除了代码重复。

## 🏗️ 架构设计

### 共享工具 (`lib/proxyUtils.ts`)

#### 核心功能
- **`handleProxyRequest`**: 统一的代理请求处理函数
- **`createCorsHeaders`**: 创建标准CORS响应头
- **`createOptionsResponse`**: 处理预检请求
- **`PROXY_CONFIGS`**: 预定义的代理配置

#### 配置结构
```typescript
interface ProxyConfig {
  baseUrl: string;      // 目标服务器URL
  serviceName: string;  // 服务名称（用于日志）
  userAgent: string;    // 自定义User-Agent
  timeout?: number;     // 请求超时时间
}
```

### 系统代理 (`/api/proxy/[...path]`)

#### 配置信息
- **目标服务器**: `http://************/api`
- **用途**: 系统相关接口（登录、用户信息等）
- **超时时间**: 30秒
- **User-Agent**: `HR-System-Proxy/1.0`

#### 路由映射
```
GET  /api/proxy/system/login    → http://************/api/system/login
POST /api/proxy/user/profile    → http://************/api/user/profile
```

### Chat代理 (`/api/chat-proxy/[...path]`)

#### 配置信息
- **目标服务器**: `http://************1:8305`
- **用途**: Chat相关接口（聊天、消息等）
- **超时时间**: 60秒（Chat可能需要更长处理时间）
- **User-Agent**: `HR-Chat-Proxy/1.0`

#### 路由映射
```
GET  /api/chat-proxy/chat/list     → http://************1:8305/chat/list
POST /api/chat-proxy/chat/send     → http://************1:8305/chat/send
PUT  /api/chat-proxy/chat/1/title  → http://************1:8305/chat/1/title
```

## 🔧 技术特性

### CORS解决方案
- ✅ 完整的CORS头支持
- ✅ 预检请求(OPTIONS)处理
- ✅ 凭据转发支持
- ✅ 自定义头部转发

### 请求处理
- **头部转发**: Authorization、Cookie、X-Requested-With等
- **超时控制**: 使用AbortSignal.timeout()
- **错误处理**: 统一的错误响应格式
- **日志记录**: 详细的请求/响应日志

### 响应处理
- **自动JSON解析**: 智能处理JSON和文本响应
- **状态码转发**: 保持原始HTTP状态码
- **头部转发**: Set-Cookie、Cache-Control等重要头部

## 📊 服务层集成

### Alova实例配置

#### 系统服务 (`lib/alova.ts`)
```typescript
baseURL: "/api/proxy"
timeout: 30000
```

#### Chat服务 (`lib/chatAlova.ts`)
```typescript
baseURL: "/api/chat-proxy"
timeout: 60000
```

### 服务方法映射

#### 系统相关服务
```typescript
// 使用系统代理
export const login = (params) => GET('/system/login', { params });
export const getUserProfile = () => GET('/user/profile');
```

#### Chat相关服务
```typescript
// 使用Chat代理
export const sendMessage = (data) => CHAT_POST('/chat/send', data);
export const getChatList = () => CHAT_GET('/chat/list');
```

## 🚀 使用示例

### 添加新代理

1. **添加配置到 `PROXY_CONFIGS`**:
```typescript
export const PROXY_CONFIGS = {
  // ... 现有配置
  newService: {
    baseUrl: 'http://new-service:8080',
    serviceName: '新服务',
    userAgent: 'HR-NewService-Proxy/1.0',
    timeout: 45000,
  },
} as const;
```

2. **创建新代理路由**:
```typescript
// app/api/new-proxy/[...path]/route.ts
import { NextRequest } from 'next/server';
import { handleProxyRequest, createOptionsResponse, PROXY_CONFIGS } from '@/lib/proxyUtils';

interface RouteParams {
  params: Promise<{ path: string[] }>;
}

export async function GET(request: NextRequest, context: RouteParams) {
  const params = await context.params;
  return handleProxyRequest('GET', request, params.path, PROXY_CONFIGS.newService);
}

// ... 其他HTTP方法

export async function OPTIONS() {
  return createOptionsResponse(PROXY_CONFIGS.newService.serviceName);
}
```

3. **创建专用Alova实例**:
```typescript
// lib/newServiceAlova.ts
export const newServiceAlovaInstance = createAlova({
  baseURL: "/api/new-proxy",
  // ... 其他配置
});
```

## 🔍 调试和监控

### 日志格式
```
🔄 系统代理GET请求: http://************/api/system/login?code=xxx
📤 发送系统请求配置: { method: 'GET', url: '...', headers: {...} }
📥 系统后端响应: { status: 200, statusText: 'OK' }
✅ 系统代理响应成功: 200
```

### 测试页面
访问 `/test-proxies` 可以测试所有代理功能。

## 🛡️ 安全考虑

1. **头部过滤**: 只转发允许的头部
2. **错误信息**: 不暴露敏感的内部错误
3. **超时控制**: 防止长时间占用资源
4. **日志记录**: 便于问题追踪和调试

## 🔧 环境变量

```env
# 后端服务URL
BACKEND_API_URL=http://************/api
CHAT_API_URL=http://************1:8305

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

## 📈 性能优化

1. **代码分离**: 每个代理独立路由，避免单点故障
2. **超时设置**: 根据服务特性设置合适的超时时间
3. **错误处理**: 快速失败，避免资源浪费
4. **头部优化**: 只转发必要的头部信息

## 🔄 维护指南

1. **添加新服务**: 按照上述模板添加新代理
2. **修改配置**: 在 `PROXY_CONFIGS` 中统一管理
3. **调试问题**: 查看控制台日志，使用测试页面
4. **更新依赖**: 保持Alova和Next.js版本更新 