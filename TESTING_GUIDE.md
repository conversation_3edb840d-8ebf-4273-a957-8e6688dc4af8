# 多会话后台生成功能测试指南

## 测试场景

### 场景1：新建会话后台生成测试

1. **步骤1**：点击"新建聊天"按钮，创建一个新的会话
2. **步骤2**：在新会话中输入一个问题并发送（例如："请详细解释人工智能的发展历程"）
3. **步骤3**：等待AI开始回答，看到深度思考内容开始输出
4. **步骤4**：在AI还在生成回答时，点击侧边栏中的一个历史会话
5. **步骤5**：等待一段时间（让AI在后台继续生成）
6. **步骤6**：返回到最新创建的会话（应该在历史会话列表的顶部）

### 预期结果

- ✅ 返回新建会话时，应该能看到AI生成的完整内容
- ✅ 不应该回到首页初始化状态
- ✅ 历史会话列表中应该显示生成状态指示器：
  - 蓝色脉动圆点：表示正在后台生成
  - 绿色圆点 + "(有新回答)"文字：表示生成完成有新内容

### 场景2：多会话并行生成测试

1. **步骤1**：创建第一个新会话，发送问题
2. **步骤2**：在AI开始回答时，创建第二个新会话，发送另一个问题
3. **步骤3**：切换到一个历史会话
4. **步骤4**：观察历史会话列表中的状态指示器
5. **步骤5**：分别返回两个新建的会话查看结果

### 预期结果

- ✅ 两个会话都应该能正常后台生成
- ✅ 状态指示器正确显示每个会话的生成状态
- ✅ 返回任一会话都能看到完整的AI回答

## 调试信息

如果遇到问题，请打开浏览器开发者工具（F12），查看Console标签页中的日志信息：

- 查找包含"开始流式消息处理"的日志
- 查找包含"保存当前会话状态"的日志  
- 查找包含"检查会话状态缓存"的日志
- 查找包含"恢复会话缓存状态"的日志

这些日志将帮助诊断会话状态管理是否正常工作。

## 常见问题排查

### 问题1：返回新建会话时变成初始化状态

**可能原因**：
- 会话ID没有正确设置
- 会话状态没有正确保存到缓存中
- 会话状态恢复逻辑有问题

**排查方法**：
1. 检查Console中是否有"保存当前会话状态"的日志
2. 检查"检查会话状态缓存"日志中的sessionId和allSessionIds
3. 确认会话ID在整个流程中保持一致

### 问题2：状态指示器不显示

**可能原因**：
- 历史会话数据没有正确更新
- 组件状态同步问题

**排查方法**：
1. 检查历史会话列表中的会话对象是否包含isGenerating和hasNewContent属性
2. 确认updateSessionGeneratingStatus和markSessionHasNewContent方法被正确调用

## 修复内容总结

本次修复主要解决了以下问题：

1. **新建会话ID设置时机**：确保在开始流式处理前就设置正确的currentChatId
2. **会话状态保存逻辑**：改进loadChatSession中的状态保存，支持新建会话
3. **状态恢复机制**：增强会话切换时的状态恢复逻辑
4. **调试日志**：添加详细的调试日志帮助问题诊断

通过这些修复，新建会话的后台生成功能应该能正常工作。
