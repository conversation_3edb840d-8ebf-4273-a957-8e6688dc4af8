import { GET, POST, PUT, DELETE } from "@/lib/alova";
import { CHAT_GET, CHAT_POST, CHAT_PUT, CHAT_DELETE } from "@/lib/chatAlova";
import type { Message, ChatHistory } from "@/store/chatStore";

// 聊天相关 API 接口类型
export interface SendMessageRequest {
  query?: string;
  user_session_id?: string;
  enable_reasoning?: boolean;
  attachment_list?: Array<{ ftype: string; content: string; fname: string }> | undefined;
}

export interface SendMessageResponse {
  message: Message;
  chatId: number;
}

export interface ChatListResponse {
  chats: ChatHistory[];
  total: number;
}

export interface CreateChatRequest {
  title?: string;
  firstMessage?: string;
}

export interface CreateChatResponse {
  chat: ChatHistory;
}

// ===============================
// Chat相关接口 - 使用Chat代理
// ===============================

// 发送消息
export const sendMessage = (
  data: SendMessageRequest = {
    query: "来帮我推荐个电池组件的负责人",
    user_session_id: "",
    enable_reasoning: true,
  }
) =>
  CHAT_POST("/agent-chat", data as unknown as Record<string, unknown>, {
    header: {
      "Content-type": "text/event-stream",
    },
  });

// EventStream版本的发送消息 - 处理流式响应
export const sendMessageStream = async (
  data: SendMessageRequest,
  onData: (chunk: string) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  onAbort?: () => void,
  signal?: AbortSignal
): Promise<void> => {
  try {
    // 获取CHAT_POST的基础配置（baseURL等）
    const response = await fetch("/api/chat-proxy/agent-chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "text/event-stream",
      },
      body: JSON.stringify(data),
      signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error("No response body available");
    }

    // 获取ReadableStream reader
    const reader = response.body
      .pipeThrough(new TextDecoderStream())
      .getReader();

    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log("Stream completed");
          onComplete?.();
          break;
        }

        // 将新数据添加到buffer
        buffer += value;

        // 处理buffer中的完整行
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留最后不完整的行

        for (const line of lines) {
          if (line.trim()) {
            processSSELine(line.trim(), onData);
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    // 先检查错误类型，再决定如何处理
    if (
      error instanceof Error &&
      (error.name === "AbortError" ||
        error.message?.includes("aborted") ||
        error.message?.includes("signal is aborted"))
    ) {
      // AbortError - 用户主动取消，不记录错误
      console.log("用户已取消请求");
      onAbort?.();
    } else {
      // 其他错误 - 记录错误并调用错误回调
      console.error("Stream error:", error);
      onError?.(error as Error);
    }
  }
};

// 处理单行SSE数据
const processSSELine = (
  line: string,
  onData: (chunk: string) => void
): void => {
  console.log("Raw line:", line);

  // 处理SSE格式的数据行
  if (line.startsWith("data: ")) {
    const jsonStr = line.substring(6); // 移除 'data: ' 前缀

    // 检查是否是结束标记
    if (jsonStr.trim() === "[DONE]") {
      console.log("Stream completed with [DONE] marker");
      return;
    }

    try {
      // 调用回调函数传递原始数据
      onData(jsonStr);
    } catch (error) {
      console.error("Failed to parse JSON:", error, "Raw data:", jsonStr);
    }
  } else if (line.startsWith(":")) {
    // SSE注释行，忽略
    console.log("SSE comment:", line);
  } else {
    // 其他格式的行
    console.log("Other line format:", line);
  }
};

// 获取预制问题
export const getRecQuestion = () => CHAT_GET("/rec-question");

// 获取聊天历史消息
export const getChatMessages = (chatId: number) =>
  CHAT_GET(`/chat/${chatId}/messages`);

// 创建新聊天
export const getGenTitle = (data: { query: string }) =>
  CHAT_POST("/gen-title", data as unknown as Record<string, unknown>);

// 更新聊天标题
export const updateChatTitle = (chatId: number, title: string) =>
  CHAT_PUT(`/chat/${chatId}/title`, { title });

// 删除聊天
export const deleteChat = (chatId: number) => CHAT_DELETE(`/chat/${chatId}`);

// 文件上传相关 - 如果是聊天文件上传，也使用Chat代理
export const uploadFile = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  return CHAT_POST("/upload", formData as unknown as Record<string, unknown>, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

// ===============================
// 系统相关接口 - 使用系统代理
// ===============================

// 用户认证相关接口类型
export interface LoginRequest {
  code: string;
  redirectUrl: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: number;
    username: string;
    email: string;
    avatar?: string;
  };
}

export interface SessionHistory {
  id?: string;
  sessionTitle?: string;
  firstQuestion?: string;
  data?: string;
}

export interface SessionRecord {
  id?: number;
  sessionId: string;
  question: string;
  answer: string;
}

// 获取用户信息
export const getUserProfile = () => GET("/user/profile");

// 用户认证相关
export const login = (
  params: LoginRequest = { code: "-200258", redirectUrl: " " }
) =>
  GET("/system/login", {
    params,
  });

export const saveHistory = (data: SessionHistory) =>
  POST("/chat/session/saveHistory", data as unknown as Record<string, unknown>);

export const delHistory = (data: { id: string }) => {
  console.log("🗑️ API: 发送删除请求:", data);
  return POST("/chat/session/delHistory", data as unknown as Record<string, unknown>);
};

// 保存一组会话记录
export const saveRecord = (data: SessionRecord) =>
  POST("/chat/session/saveRecord", data as unknown as Record<string, unknown>);

// 文件解析上传
export const fileParseUpload = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  // 移除headers配置，让浏览器自动处理Content-Type和boundary
  return POST(
    "/chat/session/fileParse",
    formData as unknown as Record<string, unknown>
  );
};

// 获取历史会话
export const getHistory = () => GET("/chat/session/listHistory", {
  params: {
    _t: Date.now() // 添加时间戳防止缓存
  }
});

export const getRecords = (params: { sessionId: string }) =>
  GET("/chat/session/listRecord", { params });

export const refreshToken = () => POST("/auth/refresh");
