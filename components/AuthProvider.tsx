"use client";

import { useEffect, useState, ReactNode } from "react";
import { usePathname } from "next/navigation";
import { handleAuthFlow, redirectToLogin } from "@/lib/auth";

interface AuthProviderProps {
  children: ReactNode;
}

const PUBLIC_ROUTES = ["/unauthorized"];

const AuthChecker = ({ children }: { children: ReactNode }) => {
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthenticatedState, setIsAuthenticatedState] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authStatus = await handleAuthFlow();
        if (!authStatus) {
          redirectToLogin();
          return;
        }
        setIsAuthenticatedState(true);
      } catch (error) {
        console.error('Auth check error:', error);
        redirectToLogin();
        return;
      } finally {
        setIsChecking(false);
      }
    };

    if (typeof window !== 'undefined') {
      checkAuth();
    }
  }, []);

  if (isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证身份...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticatedState) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">正在登录...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default function AuthProvider({ children }: AuthProviderProps) {
  const pathname = usePathname();

  if (PUBLIC_ROUTES.includes(pathname)) {
    return <>{children}</>;
  }

  return <AuthChecker>{children}</AuthChecker>;
} 