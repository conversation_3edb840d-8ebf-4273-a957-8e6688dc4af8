import { useChatStore } from "@/store/chatStore";
import Image from "next/image";
import { useState, useRef } from "react";
import { Check, X } from "lucide-react";
import { delHistory, saveHistory } from '@/services/chatService'

interface ChatHeaderProps {
  hasMessages: boolean;
  chatTitle?: string;
}

export default function ChatHeader({ hasMessages }: ChatHeaderProps) {
  const { currentTitle, setCurrentTitle,currentChatId, chatHistory, startNewChat } = useChatStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleEditClick = () => {
    setIsEditing(true);
    setEditValue(currentTitle);
    setTimeout(() => {
      inputRef.current?.focus();
      inputRef.current?.select();
    }, 0);
  };

  const handleConfirmEdit = async () => {
    setCurrentTitle(editValue);
    await saveHistory({id:currentChatId,sessionTitle:editValue})
    useChatStore.setState({
      chatHistory: chatHistory.map((item) => {
        if (item.id === currentChatId) {
          return { ...item, sessionTitle: editValue };
        }
        return item;
      }),
    })
    setIsEditing(false);
    setEditValue("");
  };

  const handleDelClick = async () => {
    await delHistory({id:currentChatId})
    useChatStore.setState({
      chatHistory: chatHistory.filter((item) => item.id !== currentChatId),
    })
  }

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
    setTimeout(() => {
      setShowAnimation(true);
    }, 10);
  };

  const handleConfirmDelete = async () => {
    setShowAnimation(false);
    setTimeout(async () => {
      await handleDelClick();
      startNewChat();
      setShowDeleteConfirm(false);
    }, 300);
  };

  const handleCancelDelete = () => {
    setShowAnimation(false);
    setTimeout(() => {
      setShowDeleteConfirm(false);
    }, 300);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditValue("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleConfirmEdit();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditValue(e.target.value);
  };

  if (!hasMessages) return null;

  return (
    <div className="flex items-center  py-4 border-b border-gray-200 bg-white">
      {/* 标题 */}
      {isEditing ? (
        <input
          ref={inputRef}
          value={editValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className="ml-6 bg-transparent border-none outline-none text-lg font-medium text-gray-800 w-full"
          aria-label="编辑标题"
        />
      ) : (
        <h2 className="ml-6 text-lg font-medium text-gray-800">{currentTitle}</h2>
      )}
      <div className="flex flex-1 items-center justify-end gap-2 mr-6">
        {/* <div className="w-7 h-7 rounded-full hover:bg-gray-400/20 flex items-center justify-center">
          <Image
            src="/upload.svg"
            alt=""
            width={0}
            height={0}
            className="w-4 h-4 cursor-pointer"
          />
        </div> */}
        {/* 编辑标题 */}
        <div 
          className="w-7 h-7 rounded-full hover:bg-gray-400/20 flex items-center justify-center"
          onClick={isEditing ? handleConfirmEdit : handleEditClick}
        >
          {isEditing ? (
            <Check className="w-4 h-4 cursor-pointer" color="#22c55e" />
          ) : (
            <Image
              src="/editor.svg"
              alt=""
              width={0}
              height={0}
              className="w-4 h-4 cursor-pointer"
            />
          )}
        </div>
        {/* 删除历史会话 */}
        <div 
          className="w-7 h-7 rounded-full hover:bg-gray-400/20 flex items-center justify-center"
          onClick={handleDeleteClick}
        >
          <Image
            src="/delete.svg"
            alt=""
            width={0}
            height={0}
            className="w-4 h-4 cursor-pointer"
          />
        </div>
      </div>

      {/* 确认删除弹窗 */}
      {showDeleteConfirm && (
        <div className={`fixed inset-0 bg-black/30 flex items-center justify-center z-50 transition-opacity duration-300 ease-in-out ${showAnimation ? 'opacity-100' : 'opacity-0'}`}>
          <div className={`bg-white rounded-lg p-6 w-96 max-w-md mx-4 transition-all duration-300 ease-in-out ${showAnimation ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
            {/* 弹窗头部 */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-900">确认删除对话</h3>
              <button
                onClick={handleCancelDelete}
                className="w-6 h-6 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
            
            {/* 弹窗内容 */}
            <div className="mb-6">
              <p className="text-gray-600 text-xs">删除后，该对话将不可恢复。确认删除吗？</p>
            </div>
            
            {/* 弹窗按钮 */}
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelDelete}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors text-xs"
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors text-xs"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
