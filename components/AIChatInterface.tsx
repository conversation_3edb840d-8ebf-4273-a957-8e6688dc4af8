"use client";

import Sidebar from "./Sidebar";
import ChatHeader from "./ChatHeader";
import ChatContent from "./ChatContent";
import ChatInput from "./ChatInput";
import { useChatStore } from "@/store/chatStore";

export default function AIChatInterface() {
  const {
    messages,
    inputValue,
    sidebarCollapsed,
    isLoading,
    setInputValue,
    setSidebarCollapsed,
  } = useChatStore();



  return (
    <div className="flex h-screen bg-gray-50">
      {/* 侧边栏 */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        setSidebarCollapsed={setSidebarCollapsed}
      />
      {/* 外层div */}
      <div className={`flex-1 flex flex-col relative`}>
        {/* Background layer with opacity */}
        <div className="absolute inset-0 bg-[url('/bg.png')] bg-cover bg-center bg-repeat opacity-30 z-0"></div>
        
        {/* Content layer */}
        <div className={`relative z-10 flex flex-col h-full ${messages.length > 0 ? 'justify-end' : 'justify-center'}`}>
          {/* 头部 */}
          <ChatHeader hasMessages={messages.length > 0} />
          {/* 内容 */}
          <ChatContent messages={messages} />

          {/* 输入框 */}
          <ChatInput
            inputValue={inputValue}
            setInputValue={setInputValue}
            hasMessages={messages.length > 0}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
