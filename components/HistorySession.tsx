"use client";
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import Image from "next/image";
import { useChatStore } from "@/store/chatStore";
import type { ChatHistory } from "@/store/chatStore";
import { Ellipsis, X, Check, Edit3, Trash2 } from "lucide-react";
import { delHistory, saveHistory } from "@/services/chatService";

// useHasHydrated hook 实现
const useHasHydrated = () => {
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    const unsubFinish = useChatStore.persist.onFinishHydration(() =>
      setHydrated(true)
    );

    setHydrated(useChatStore.persist.hasHydrated());

    return () => {
      unsubFinish();
    };
  }, []);

  return hydrated;
};

// 防抖函数类型定义
type DebounceFunction = (...args: unknown[]) => void;

// 防抖函数实现
function debounce(func: DebounceFunction, wait: number): DebounceFunction {
  let timeout: NodeJS.Timeout | null = null;
  return function executedFunction(...args: unknown[]) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

const HistorySession: React.FC<{ sidebarCollapsed: boolean }> = ({
  sidebarCollapsed,
}) => {
  const {
    currentChatId,
    chatHistory,
    setChatHistory,
    loadChatSession,
    setCurrentChatId,
    setCurrentTitle,
    setUploadResult,
    setInputValue,
    startNewChat,
  } = useChatStore();
  const hasHydrated = useHasHydrated();

  // Scrollbar visibility state
  const containerRef = useRef<HTMLDivElement>(null);
  const [showScrollbar, setShowScrollbar] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const isHoveringRef = useRef(false);

  // Menu-related states
  const [activeMenuId, setActiveMenuId] = useState<string | null>(null);
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingChatId, setDeletingChatId] = useState<string | null>(null);
  const [showAnimation, setShowAnimation] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);

  useEffect(() => {
    const loadHistory = async () => {
      if (!hasHydrated || chatHistory.length > 0) {
        return;
      }
      console.log("🚀 初始化加载历史数据...");
      setChatHistory();
    };
    loadHistory();
  }, [hasHydrated, chatHistory.length, setChatHistory]);

  // Monitor chatHistory changes
  useEffect(() => {
    console.log("📊 历史列表状态变化:", {
      length: chatHistory.length,
      ids: chatHistory.map(chat => chat.id),
      titles: chatHistory.map(chat => chat.sessionTitle)
    });
  }, [chatHistory]);

  // Sync isHovering state to ref
  useEffect(() => {
    isHoveringRef.current = isHovering;
  }, [isHovering]);

  // Scrollbar visibility logic
  const debouncedHideScrollbar = useMemo(() => {
    return debounce(() => {
      if (!isHoveringRef.current) {
        setShowScrollbar(false);
      }
    }, 1500);
  }, []);

  const handleScroll = useCallback(() => {
    setShowScrollbar(true);
    debouncedHideScrollbar();
  }, [debouncedHideScrollbar]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [handleScroll]);

  // Handle outside click to close menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setActiveMenuId(null);
        setMenuPosition(null);
      }
    };

    if (activeMenuId) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [activeMenuId]);

  // Handle window resize to recalculate menu position
  useEffect(() => {
    const handleResize = () => {
      if (activeMenuId && menuPosition) {
        setActiveMenuId(null);
        setMenuPosition(null);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [activeMenuId, menuPosition]);

  const handleMouseEnter = () => {
    setIsHovering(true);
    setShowScrollbar(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
    debouncedHideScrollbar();
  };

  const handleChatClick = (chat: ChatHistory) => {
    if (chat.id === currentChatId) return; // Avoid reloading the same chat
    setCurrentChatId(chat.id!);
    setCurrentTitle(chat.sessionTitle);
    setUploadResult(chat.data ? JSON.parse(chat.data!) : "");
    loadChatSession(chat.id!);
    setInputValue("");
  };

  // Helper function to get element position relative to viewport
  const getElementPosition = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top,
      left: rect.left,
      right: rect.right,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height,
    };
  };

  // Calculate optimal menu position with boundary detection
  const calculateMenuPosition = (triggerElement: HTMLElement) => {
    const triggerPos = getElementPosition(triggerElement);
    const menuWidth = 100; // w-32 = 128px
    const menuHeight = 80; // Approximate height for 2 items
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const margin = 8; // Margin from screen edges

    let top = triggerPos.bottom + 4; // 4px gap below trigger
    let left = triggerPos.right - menuWidth + 100; // Align right edge with trigger
                             
    // Boundary detection and adjustment
    // Check right boundary
    if (left < margin) {
      left = triggerPos.left; // Align left edge with trigger
    }
    
    // Check left boundary (in case menu is too wide)
    if (left + menuWidth > viewportWidth - margin) {
      left = viewportWidth - menuWidth - margin;
    }

    // Check bottom boundary
    if (top + menuHeight > viewportHeight - margin) {
      top = triggerPos.top - menuHeight - 4; // Position above trigger
    }

    // Ensure menu doesn't go above viewport
    if (top < margin) {
      top = margin;
    }

    return { top, left };
  };

  const handleMenuToggle = (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (activeMenuId === chatId) {
      setActiveMenuId(null);
      setMenuPosition(null);
    } else {
      const triggerElement = event.currentTarget as HTMLElement;
      const position = calculateMenuPosition(triggerElement);
      setMenuPosition(position);
      setActiveMenuId(chatId);
    }
  };

  const handleRenameClick = (chat: ChatHistory, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingChatId(chat.id!);
    setEditingTitle(chat.sessionTitle);
    setActiveMenuId(null);
    setMenuPosition(null);
  };

  const handleDeleteClick = (chat: ChatHistory, event: React.MouseEvent) => {
    event.stopPropagation();
    setDeletingChatId(chat.id!);
    setActiveMenuId(null);
    setMenuPosition(null);
    setShowDeleteConfirm(true);
    setTimeout(() => {
      setShowAnimation(true);
    }, 10);
  };

  const handleConfirmRename = async () => {
    if (!editingChatId) return;
    
    // Check if input is empty or only whitespace
    if (!editingTitle.trim()) {
      handleCancelRename();
      return;
    }
    
    try {
      await saveHistory({ id: editingChatId, sessionTitle: editingTitle });
      setEditingChatId(null);
      setEditingTitle("");
      // Update current title if editing current chat
      if (editingChatId === currentChatId) {
        setCurrentTitle(editingTitle);
      }
      
      // Update chat history
      await setChatHistory();
      
      
    } catch (error) {
      console.error("Failed to rename chat:", error);
      // Clear editing state even if operation fails
      setEditingChatId(null);
      setEditingTitle("");
    }
  };

  const handleCancelRename = () => {
    setEditingChatId(null);
    setEditingTitle("");
  };

  const handleConfirmDelete = async () => {
    if (!deletingChatId) return;
    
    // Log state before deletion
    console.log("🗑️ 开始删除操作:", {
      deletingChatId,
      currentChatId,
      chatHistoryLength: chatHistory.length,
      chatHistoryIds: chatHistory.map(chat => chat.id)
    });
    
    setShowAnimation(false);
    setTimeout(async () => {
      try {
        // Call delete API
        console.log("📡 调用删除API:", { id: deletingChatId });
        const deleteResult = await delHistory({ id: deletingChatId });
        console.log("✅ 删除API响应:", deleteResult);
        
        // If deleting current chat, start new chat
        if (deletingChatId === currentChatId) {
          console.log("🔄 删除的是当前会话，开始新会话");
          startNewChat();
        }
        
        // Update chat history from server
        console.log("📡 开始获取最新历史数据...");
        await setChatHistory();
        
        // Log state after refresh
        const updatedHistory = useChatStore.getState().chatHistory;
        console.log("✅ 历史数据刷新完成:", {
          newChatHistoryLength: updatedHistory.length,
          newChatHistoryIds: updatedHistory.map(chat => chat.id),
          deletedIdStillExists: updatedHistory.some(chat => chat.id === deletingChatId)
        });
        
        setShowDeleteConfirm(false);
        setDeletingChatId(null);
      } catch (error) {
        console.error("❌ 删除操作失败:", error);
        setShowDeleteConfirm(false);
        setDeletingChatId(null);
      }
    }, 300);
  };

  const handleCancelDelete = () => {
    setShowAnimation(false);
    setTimeout(() => {
      setShowDeleteConfirm(false);
      setDeletingChatId(null);
    }, 300);
  };

  const handleRenameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleConfirmRename();
    } else if (e.key === "Escape") {
      handleCancelRename();
    }
  };

  if (!hasHydrated) {
    return null;
  }

  // Log current render state
  console.log("🎨 组件渲染:", {
    chatHistoryLength: chatHistory.length,
    currentChatId,
    hasHydrated,
    editingChatId,
    deletingChatId,
    showDeleteConfirm
  });

  return (
    <>
      {/* 历史会话列表 */}
      <div className="flex-1 p-4 overflow-hidden">
        <div
          className={`w-full flex items-center gap-2 text-sm text-gray-700 rounded-md transition-colors ${
            sidebarCollapsed ? "justify-center" : "px-3 py-2"
          } ${chatHistory.length === 0 ? "text-gray-700/30" : ""}`}
          title={sidebarCollapsed ? "历史会话" : ""}
        >
          <Image src="/history.svg" alt="" width={16} height={16} />
          {!sidebarCollapsed && <span>历史会话</span>}
        </div>
        {!sidebarCollapsed && (
          <div
            ref={containerRef}
            className={`space-y-2 mt-3 max-h-[calc(100vh-250px)] overflow-y-auto history-scroll-container  ${
              showScrollbar ? "show-scrollbar" : ""
            }`}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {chatHistory.map((chat) => (
              <div
                key={chat.id}
                onClick={() => editingChatId !== chat.id && handleChatClick(chat)}
                className={`flex flex-col px-3 py-2 text-sm rounded-md transition-colors group ${
                  editingChatId === chat.id
                    ? "" // 编辑状态无背景无边框
                    : currentChatId === chat.id
                    ? "bg-blue-50 border-l-2 border-blue-500 cursor-pointer" // 当前聊天样式
                    : activeMenuId === chat.id
                    ? "cursor-pointer" // 菜单激活时只保持cursor-pointer，无hover效果
                    : "hover:bg-gray-100 cursor-pointer" // 默认hover样式
                }`}
              >
                {editingChatId === chat.id ? (
                  // 编辑状态：input撑满整个容器
                  <input
                    value={editingTitle}
                    onChange={(e) => setEditingTitle(e.target.value)}
                    onKeyDown={handleRenameKeyDown}
                    onBlur={handleConfirmRename}
                    onClick={(e) => e.stopPropagation()}
                    className="text-xs text-gray-900 bg-white border border-blue-500 rounded-xl px-2 py-1 outline-none w-full focus:ring-2 focus:ring-blue-200"
                    autoFocus
                  />
                ) : (
                  // 非编辑状态：正常布局带菜单按钮
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="truncate text-xs text-gray-900">
                        {chat.sessionTitle}
                      </span>
                      {/* 生成状态指示器 */}
                      {chat.isGenerating && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse flex-shrink-0" title="AI正在生成回答..."></div>
                      )}
                      {/* 新内容指示器 */}
                      {chat.hasNewContent && !chat.isGenerating && (
                        <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" title="有新的回答内容"></div>
                      )}
                    </div>
                    <div
                      className="w-7 h-5 hover:bg-white rounded-lg flex items-center justify-center relative"
                    >
                      <Ellipsis 
                        className="w-6 h-4 cursor-pointer" 
                        onClick={(e) => handleMenuToggle(chat.id!, e)}
                      />
                      
                      {/* Context Menu */}
                      {activeMenuId === chat.id && menuPosition && (
                        <div 
                          ref={menuRef}
                          className="fixed bg-white border border-gray-200 rounded-lg shadow-lg py-2 w-26 z-50"
                          style={{
                            top: `${menuPosition.top}px`,
                            left: `${menuPosition.left}px`,
                          }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          <button
                            onClick={(e) => handleRenameClick(chat, e)}
                            className="w-full px-3 py-2 text-left text-xs text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                          >
                            <Edit3 className="w-3 h-3" />
                            重命名
                          </button>
                          <button
                            onClick={(e) => handleDeleteClick(chat, e)}
                            className="w-full px-3 py-2 text-left text-xs text-red-600 hover:bg-gray-100 flex items-center gap-2"
                          >
                            <Trash2 className="w-3 h-3" />
                            删除
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Popup */}
      {showDeleteConfirm && (
        <div className={`fixed inset-0 bg-black/30 flex items-center justify-center z-50 transition-opacity duration-300 ease-in-out ${showAnimation ? 'opacity-100' : 'opacity-0'}`}>
          <div className={`bg-white rounded-lg p-6 w-96 max-w-md mx-4 transition-all duration-300 ease-in-out ${showAnimation ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
            {/* Popup Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium text-gray-900">确认删除对话</h3>
              <button
                onClick={handleCancelDelete}
                className="w-6 h-6 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors"
              >
                <X className="w-4 h-4 text-gray-500" />
              </button>
            </div>
            
            {/* Popup Content */}
            <div className="mb-6">
              <p className="text-gray-600 text-xs">删除后，该对话将不可恢复。确认删除吗？</p>
            </div>
            
            {/* Popup Buttons */}
            <div className="flex justify-end gap-3">
              <button
                onClick={handleCancelDelete}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors text-xs"
              >
                取消
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors text-xs"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default HistorySession;
