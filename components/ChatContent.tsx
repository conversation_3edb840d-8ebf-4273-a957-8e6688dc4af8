"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import type { Components } from "react-markdown";
import "highlight.js/styles/github-dark.css";
import {
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import type { Message } from "@/store/chatStore";
import { useChatStore } from "@/store/chatStore";
import LottieAnimation from "@/components/LottieAnimation";
import loadingAnimationData from "@/components/loading.json";


interface ChatContentProps {
  messages: Message[];
}

// 流式打字机效果Hook - 支持实时更新的文本
const useStreamingTypewriter = (
  streamingText: string,
  isComplete: boolean,
  speed: number = 50,
  onComplete?: () => void,
  shouldStop?: boolean,
  onStop?: (duration: number) => void
) => {
  console.log('useStreamingTypewriter被调用，参数:', {
    streamingText: streamingText || '[空字符串]',
    streamingTextLength: streamingText?.length || 0,
    isComplete,
    speed
  });
  
  const [displayText, setDisplayText] = useState("");
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [duration, setDuration] = useState<number>(0);

  useEffect(() => {
    console.log('useStreamingTypewriter useEffect触发:', {
      streamingText: streamingText || '[空字符串]',
      streamingTextLength: streamingText?.length || 0,
      displayText: displayText || '[空字符串]',
      displayTextLength: displayText?.length || 0,
      isComplete,
      startTime,
      isTypingComplete,
      shouldStop
    });

    // 检查是否需要停止
    if (shouldStop && !isTypingComplete && startTime) {
      console.log('检测到停止信号，立即停止打字机');
      setIsTypingComplete(true);
      const stopTime = Date.now();
      const calculatedDuration = Math.round((stopTime - startTime) / 1000 * 10) / 10;
      setDuration(calculatedDuration);
      onStop?.(calculatedDuration);
      return;
    }

    if (!streamingText) {
      console.log('streamingText为空，重置状态');
      setDisplayText("");
      setIsTypingComplete(false);
      setDuration(0);
      setStartTime(null);
      return;
    }

    // 如果是第一次接收到文本，记录开始时间
    if (!startTime) {
      console.log('首次接收文本，设置开始时间');
      setStartTime(Date.now());
    }

    // 如果流式数据完成且当前显示的文本少于总文本，继续打字机效果
    if (displayText.length < streamingText.length && !shouldStop) {
      console.log('开始打字机效果，当前长度:', displayText.length, '目标长度:', streamingText.length);
      const timer = setTimeout(() => {
        // 恢复逐字显示逻辑
        const newDisplayText = streamingText.slice(0, displayText.length + 1);
        console.log('打字机更新displayText:', newDisplayText);
        setDisplayText(newDisplayText);
        

      }, speed); // 恢复使用speed参数

      return () => clearTimeout(timer);
    }

    // 如果显示完成且流式数据也完成，标记为完成
    if (isComplete && displayText.length >= streamingText.length && !isTypingComplete) {
      console.log('打字机完成，触发onComplete');
      setIsTypingComplete(true);
      if (startTime) {
        const end = Date.now();
        setDuration(Math.round((end - startTime) / 1000 * 10) / 10);
      }
      console.log('useStreamingTypewriter完成，立即触发onComplete回调');
      // 立即触发onComplete，不再延迟
      onComplete?.();
    }
  }, [streamingText, displayText, isComplete, speed, onComplete, startTime, isTypingComplete, shouldStop, onStop]);

  const result = { displayText, isComplete: isTypingComplete, duration };
  console.log('useStreamingTypewriter返回值:', result);
  return result;
};

// 思考过程的自定义组件（特殊样式）
const thinkingComponents: Components = {
  h1: ({ children }) => (
    <h1 className="text-xs font-bold mt-4 mb-2 text-gray-700">{children}</h1>
  ),
  h2: ({ children }) => (
    <h2 className="text-xs font-bold mt-3 mb-2 text-gray-700">{children}</h2>
  ),
  h3: ({ children }) => (
    <h3 className="text-xs font-semibold mt-2 mb-1 text-gray-700">{children}</h3>
  ),
  p: ({ children }) => <p className="mb-2 text-gray-600 text-xs">{children}</p>,
  code: ({ children, className }) => {
    const isInline = !className?.includes("language-");
    return isInline ? (
      <code className="bg-gray-200 text-gray-800 px-1 py-0.5 rounded text-xs font-mono">
        {children}
      </code>
    ) : (
      <code className={`${className} font-mono text-xs`}>{children}</code>
    );
  },
  pre: ({ children }) => (
    <pre className="bg-gray-100 text-gray-800 rounded p-2 my-2 overflow-x-auto text-xs">
      {children}
    </pre>
  ),
  ul: ({ children }) => (
    <ul className="list-disc ml-4 mb-2 space-y-1">{children}</ul>
  ),
  ol: ({ children }) => (
    <ol className="list-decimal ml-4 mb-2 space-y-1">{children}</ol>
  ),
  li: ({ children }) => <li className="text-gray-600 text-xs">{children}</li>,
  blockquote: ({ children }) => (
    <blockquote className="border-l-2 border-gray-300 pl-2 py-1 mb-2 bg-gray-50 italic text-gray-600 text-xs">
      {children}
    </blockquote>
  ),
  strong: ({ children }) => (
    <strong className="font-bold text-gray-700">{children}</strong>
  ),
  em: ({ children }) => <em className="italic text-gray-600">{children}</em>,
};

// 时间估算函数
const calculateEstimatedDuration = (content: string): number => {
  if (!content) return 0;
  
  // 基础字符分析
  const chineseChars = (content.match(/[\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef]/g) || []).length;
  const englishChars = content.length - chineseChars;
  
  // 内容结构分析
  const codeBlocks = (content.match(/```[\s\S]*?```/g) || []).length;
  const listItems = (content.match(/^[\s]*[-*+]\s/gm) || []).length;
  const numberedListItems = (content.match(/^[\s]*\d+\.\s/gm) || []).length;
  const headings = (content.match(/^#+\s/gm) || []).length;
  const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
  
  // 基础时间计算（提高输出速度：中文30字符/秒，英文60字符/秒）
  const baseTime = (chineseChars / 30) + (englishChars / 60);
  
  // 结构复杂度加权
  const structureTime = (codeBlocks * 2) + 
                       ((listItems + numberedListItems) * 0.5) + 
                       (headings * 0.3);
  
  // 总估算时间
  const totalEstimated = baseTime + structureTime;
  
  // 动态上限机制
  let dynamicLimit;
  if (content.length <= 200) {
    dynamicLimit = 15; // 短内容：15秒上限
  } else if (content.length <= 800) {
    dynamicLimit = 30; // 中等内容：30秒上限
  } else {
    dynamicLimit = 45; // 长内容：45秒上限
  }
  
  // 最终时间（最小1秒，动态上限，保留1位小数）
  const finalTime = Math.max(1, Math.min(dynamicLimit, Math.round(totalEstimated * 10) / 10));
  
  // 调试日志
  console.log('时间估算详情:', {
    contentLength: content.length,
    chineseChars,
    englishChars,
    structure: { codeBlocks, listItems, numberedListItems, headings, paragraphs },
    baseTime: Math.round(baseTime * 10) / 10,
    structureTime: Math.round(structureTime * 10) / 10,
    totalEstimated: Math.round(totalEstimated * 10) / 10,
    dynamicLimit,
    finalTime
  });
  
  return finalTime;
};

// 思考过程组件
const ThinkingProcess = ({
  messageId,
  thinkingProcess,
  onComplete,
}: {
  messageId: number;
  thinkingProcess: Message['thinkingProcess'];
  onComplete?: () => void;
}) => {
  const { updateMessage } = useChatStore();
  const isHistory = thinkingProcess?.isComplete && !thinkingProcess?.isStreaming;

  // 智能默认展开：流式消息自动展开，已完成消息默认收起
  const [isExpanded, setIsExpanded] = useState(isHistory || thinkingProcess?.isStreaming || !!thinkingProcess?.content);
  const [userManuallyToggled, setUserManuallyToggled] = useState(false);

  // 自动展开流式消息（只有在用户没有手动操作的情况下）
  useEffect(() => {
    if (!userManuallyToggled && thinkingProcess?.isStreaming) {
      console.log('自动展开流式思考过程');
      setIsExpanded(true);
    }
  }, [thinkingProcess?.isStreaming, userManuallyToggled]);

  // 获取停止状态
  const { currentStreamingMessage } = useChatStore();
  const shouldStop = currentStreamingMessage?.isStopped || thinkingProcess?.isStopped || false;

  const typewriterResult = useStreamingTypewriter(
    isHistory ? "" : (thinkingProcess?.content ?? ""),
    thinkingProcess?.isComplete || false,
    10,
    () => {
      console.log('思考过程打字机效果完成，准备触发onComplete回调');
      onComplete?.();
    },
    shouldStop,
    (stopDuration) => {
      console.log('思考过程被停止，用时:', stopDuration, '秒');
    }
  );

  const displayText = isHistory ? thinkingProcess?.content ?? "" : typewriterResult.displayText;
  const isComplete = isHistory ? true : typewriterResult.isComplete;
  
  // 优化duration计算：实际duration > 估算duration > 0
  const getDuration = (): number => {
    // 优先使用实际记录的duration
    if (thinkingProcess?.duration && thinkingProcess.duration > 0) {
      return thinkingProcess.duration;
    }
    
    // 如果是历史消息且没有duration，使用估算值
    if (isHistory && thinkingProcess?.content) {
      return calculateEstimatedDuration(thinkingProcess.content);
    }
    
    // 流式消息使用打字机的duration
    if (!isHistory && typewriterResult.duration > 0) {
      return typewriterResult.duration;
    }
    
    return 0;
  };
  
  const duration = getDuration();

  // 当打字机完成时，更新store中的duration
  useEffect(() => {
    if (typewriterResult.isComplete && typewriterResult.duration > 0 && !thinkingProcess?.duration) {
      console.log('思考过程打字机完成，更新duration:', typewriterResult.duration);
      updateMessage(messageId, {
        thinkingProcess: {
          ...(thinkingProcess!),
          duration: typewriterResult.duration,
        }
      });
    }
  }, [typewriterResult.isComplete, typewriterResult.duration, thinkingProcess, messageId, updateMessage]);

  // 为历史消息补充估算duration
  useEffect(() => {
    if (isHistory && thinkingProcess?.content && !thinkingProcess?.duration) {
      const estimatedDuration = calculateEstimatedDuration(thinkingProcess.content);
      console.log('为历史思考过程补充估算duration:', estimatedDuration);
      updateMessage(messageId, {
        thinkingProcess: {
          ...(thinkingProcess!),
          duration: estimatedDuration,
        }
      });
    }
  }, [isHistory, thinkingProcess?.content, thinkingProcess?.duration, messageId, updateMessage]);

  // 调试日志
  useEffect(() => {
    console.log('ThinkingProcess状态变化:', {
      isExpanded,
      isStreaming: thinkingProcess?.isStreaming,
      isComplete: thinkingProcess?.isComplete,
      contentLength: thinkingProcess?.content.length ?? 0,
      typewriterIsComplete: typewriterResult.isComplete,
      typewriterDisplayLength: typewriterResult.displayText.length,
      userManuallyToggled,
      isHistory,
      shouldStop
    });
  }, [isExpanded, thinkingProcess, userManuallyToggled, isHistory, typewriterResult.isComplete, typewriterResult.displayText.length, shouldStop]);

  // 处理用户手动展开/收起
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    setUserManuallyToggled(true);
  };

  // 根据不同状态显示不同的文案
  const getStatusText = () => {
    // 优先级1: 已停止且未展开（或展开但无时间记录）
    if (shouldStop && (!isExpanded || duration === 0)) {
      return "深度思考已停止";
    }
    // 优先级2: 已停止且已展开且有时间记录
    if (shouldStop && isExpanded && duration > 0) {
      return `（用时 ${duration} 秒，已停止）`;
    }
    // 优先级3: 思考过程完成了全部输出（正常完成，未停止）
    if ((!shouldStop && isComplete && duration > 0 && (thinkingProcess?.isComplete || thinkingProcess?.duration)) || isHistory) {
      return `已完成深度思考（用时 ${duration} 秒）`;
    }
    // 优先级4: 正在进行思考过程（流式中或者还未完成全部输出）
    if (thinkingProcess?.isStreaming || (!isComplete && thinkingProcess?.content)) {
      return "思考中...";
    }
    return "";
  };

  return (
    <div className="mb-4">
      {/* 思考过程的展开/收起按钮 */}
      <button
        onClick={handleToggleExpanded}
        className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 transition-colors mb-4"
      >
        <Image
          src="/logo.svg"
          width={16}
          height={16}
          alt="思考"
          className="w-4 h-4"
        />
        {/* 思考过程状态 */}
        <span>
          {getStatusText()}
        </span>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4" />
        ) : (
          <ChevronDown className="w-4 h-4" />
        )}
      </button>
      {/* 深度思考内容 */}
      {isExpanded && (
        <div className="pl-4 border-l-2 border-gray-200">
          <div className="prose prose-sm max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight, rehypeRaw]}
              components={thinkingComponents}
            >
              {displayText}
            </ReactMarkdown>
            {!isComplete && thinkingProcess?.isStreaming && (
              <span className="animate-pulse text-gray-400">|</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 用户消息的自定义组件（白色主题）
const userComponents: Components = {
  h1: ({ children }) => (
    <h1 className="text-xl font-bold mt-4 mb-2 text-white">{children}</h1>
  ),
  h2: ({ children }) => (
    <h2 className="text-lg font-bold mt-3 mb-2 text-white">{children}</h2>
  ),
  h3: ({ children }) => (
    <h3 className="text-base font-semibold mt-2 mb-1 text-white">
      {children}
    </h3>
  ),
  p: ({ children }) => <p className="mb-2 text-white">{children}</p>,
  code: ({ children, className }) => {
    const isInline = !className?.includes("language-");
    return isInline ? (
      <code className="bg-blue-400 text-white px-1 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    ) : (
      <code className={`${className} font-mono`}>{children}</code>
    );
  },
  pre: ({ children }) => (
    <pre className="bg-gray-800 text-gray-100 rounded-lg p-4 my-3 overflow-x-auto">
      {children}
    </pre>
  ),
  a: ({ children, href }) => (
    <a href={href} className="text-blue-200 hover:text-blue-100 underline">
      {children}
    </a>
  ),
};

// 助手消息的自定义组件（浅色主题）
const assistantComponents: Components = {
  h1: ({ children }) => (
    <h1 className="text-sm font-bold mt-6 mb-4 text-gray-900">{children}</h1>
  ),
  h2: ({ children }) => (
    <h2 className="text-sm font-bold mt-5 mb-3 text-gray-900">{children}</h2>
  ),
  h3: ({ children }) => (
    <h3 className="text-sm font-semibold mt-4 mb-2 text-gray-900">
      {children}
    </h3>
  ),
  p: ({ children }) => <p className="mb-4 text-gray-800 text-sm">{children}</p>,
  code: ({ children, className }) => {
    const isInline = !className?.includes("language-");
    return isInline ? (
      <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    ) : (
      <code className={`${className} font-mono`}>{children}</code>
    );
  },
  pre: ({ children }) => (
    <pre className="bg-gray-900 text-gray-100 rounded-lg p-4 my-4 overflow-x-auto border">
      {children}
    </pre>
  ),
  ul: ({ children }) => (
    <ul className="list-disc ml-6 mb-4 space-y-1">{children}</ul>
  ),
  ol: ({ children }) => (
    <ol className="list-decimal ml-6 mb-4 space-y-1">{children}</ol>
  ),
  li: ({ children }) => <li className="text-gray-800 text-sm">{children}</li>,
  blockquote: ({ children }) => (
    <blockquote className="border-l-4 border-gray-300 pl-4 py-2 mb-4 bg-gray-50 italic text-gray-700 text-sm">
      {children}
    </blockquote>
  ),
  a: ({ children, href }) => (
    <a href={href} className="text-blue-600 hover:text-blue-800 underline">
      {children}
    </a>
  ),
  strong: ({ children }) => (
    <strong className="font-bold text-gray-900 text-sm">{children}</strong>
  ),
  em: ({ children }) => <em className="italic text-gray-800 text-sm">{children}</em>,
  hr: () => <hr className="border-t border-gray-300 my-6" />,
  table: ({ children }) => (
    <div className="overflow-x-auto mb-4">
      <table className="w-full border-collapse border border-gray-300">
        {children}
      </table>
    </div>
  ),
  th: ({ children }) => (
    <th className="border border-gray-300 px-3 py-2 bg-gray-100 font-semibold text-left text-sm">
      {children}
    </th>
  ),
  td: ({ children }) => (
    <td className="border border-gray-300 px-3 py-2 text-gray-800 text-sm">
      {children}
    </td>
  ),
};

const StaticMessage = ({ message }: { message: Message }) => {
  const { isDeepMindEnabled } = useChatStore();
  
  return (
    <>
      {isDeepMindEnabled && message.thinkingProcess && message.thinkingProcess.content && (
        <ThinkingProcess
          messageId={message.id}
          thinkingProcess={message.thinkingProcess}
        />
      )}
      <div className="px-4 py-3 max-w-[80%]">
        <div className="prose max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight, rehypeRaw]}
            components={assistantComponents}
          >
            {message.content}
          </ReactMarkdown>
        </div>
      </div>
    </>
  );
};

// 流式消息组件 - 管理思考过程和回答内容的时序
const StreamingMessage = ({ message }: { message: Message }) => {
  const [canShowAnswer, setCanShowAnswer] = useState(false);
  
  // 获取队列处理方法
  const { currentStreamingMessage, isDeepMindEnabled } = useChatStore();
  
  // 当思考过程完成时，启动Messages队列处理
  const handleThinkingComplete = useCallback(() => {
    console.log('思考过程打字机完成，开始处理答案');
    
    // 验证当前状态
    const { currentStreamingMessage, startAnswerProcessing } = useChatStore.getState();
    if (!currentStreamingMessage) {
      console.error('handleThinkingComplete: currentStreamingMessage不存在');
      return;
    }
    
    console.log('handleThinkingComplete验证状态:', {
      isThinkingComplete: currentStreamingMessage.isThinkingComplete,
      tempMessagesCount: currentStreamingMessage.tempMessagesCache.length,
      isReadyForAnswers: currentStreamingMessage.isReadyForAnswers
    });
    
    // 立即设置canShowAnswer并开始答案处理
    setCanShowAnswer(true);
    
    // 记录回答开始时间
    if (!currentStreamingMessage.answerStartTime) {
      useChatStore.setState({
        currentStreamingMessage: {
          ...currentStreamingMessage,
          answerStartTime: Date.now(),
        },
      });
    }
    
    // 启动答案处理流程
    console.log('启动答案处理流程，缓存消息数量:', currentStreamingMessage.tempMessagesCache.length);
    startAnswerProcessing();
  }, []);

  // 监听Messages队列处理
  useEffect(() => {
    if (currentStreamingMessage?.isProcessingMessages && 
        currentStreamingMessage.pendingMessages.length > 0) {
      console.log('检测到Messages队列处理中，剩余消息数:', currentStreamingMessage.pendingMessages.length);
    }
  }, [currentStreamingMessage?.isProcessingMessages, currentStreamingMessage?.pendingMessages]);

  // 监听思考过程状态变化和isReadyForAnswers状态
  useEffect(() => {
    console.log('StreamingMessage思考过程状态变化:', {
      hasThinkingProcess: !!message.thinkingProcess,
      isThinkingStreaming: message.thinkingProcess?.isStreaming,
      isThinkingComplete: message.thinkingProcess?.isComplete,
      canShowAnswer,
      isReadyForAnswers: currentStreamingMessage?.isReadyForAnswers,
      messageIsStreaming: message.isStreaming
    });
  }, [message.thinkingProcess?.isStreaming, message.thinkingProcess?.isComplete, message.thinkingProcess, canShowAnswer, message.isStreaming, currentStreamingMessage?.isReadyForAnswers]);

  // 监听isReadyForAnswers状态，自动开始显示答案
  useEffect(() => {
    if (currentStreamingMessage?.isReadyForAnswers && !canShowAnswer) {
      console.log('检测到isReadyForAnswers为true，自动开始显示答案');
      setCanShowAnswer(true);
    }
  }, [currentStreamingMessage?.isReadyForAnswers, canShowAnswer]);

  // 获取实时答案内容 - 直接使用累积的答案内容，无打字机效果
  const getAnswerContent = () => {
    if (canShowAnswer && currentStreamingMessage?.answerStarted) {
      // 答案阶段：直接显示实时累积的内容
      return currentStreamingMessage.answerContent || "";
    }
    // 非答案阶段：显示空内容
    return "";
  };

  const answerDisplayText = getAnswerContent();

  // 判断是否应该显示loading动画
  const shouldShowLoading = !isDeepMindEnabled && 
                           message.isStreaming && 
                           !currentStreamingMessage?.answerStarted && 
                           currentStreamingMessage?.id === message.id;

  // 调试日志
  useEffect(() => {
    console.log('StreamingMessage渲染状态:', {
      canShowAnswer,
      isStreaming: message.isStreaming,
      hasStreamingContent: !!message.streamingContent,
      thinkingIsStreaming: message.thinkingProcess?.isStreaming,
      thinkingIsComplete: message.thinkingProcess?.isComplete,
      isReadyForAnswers: currentStreamingMessage?.isReadyForAnswers,
      answerStarted: currentStreamingMessage?.answerStarted,
      tempMessagesCount: currentStreamingMessage?.tempMessagesCache.length || 0,
      pendingMessagesCount: currentStreamingMessage?.pendingMessages.length || 0,
      isProcessingMessages: currentStreamingMessage?.isProcessingMessages || false,
      accumulatedContent: currentStreamingMessage?.answerContent || "",
      accumulatedContentLength: currentStreamingMessage?.answerContent?.length || 0,
      answerDisplayText: answerDisplayText || "",
      answerDisplayTextLength: answerDisplayText?.length || 0,
      renderingContent: message.isStreaming ? answerDisplayText : message.content
    });
  }, [canShowAnswer, message.isStreaming, message.streamingContent, message.thinkingProcess, message.content, currentStreamingMessage, answerDisplayText]);

  return (
    <>
      {isDeepMindEnabled && message.thinkingProcess && (
        <ThinkingProcess
          messageId={message.id}
          thinkingProcess={message.thinkingProcess}
          onComplete={handleThinkingComplete}
        />
      )}
      
      <div className="px-4 py-3 max-w-[80%]">
        {shouldShowLoading ? (
          <div className="flex items-center justify-start py-4">
            <LottieAnimation
              animationData={loadingAnimationData}
              className="w-12 h-3"
            />
          </div>
        ) : (
          <div className="prose max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeHighlight, rehypeRaw]}
              components={assistantComponents}
            >
              {message.isStreaming ? (
                answerDisplayText
              ) : (
                message.content
              )}
            </ReactMarkdown>
            {message.isStreaming && canShowAnswer && (
              <span className="animate-pulse text-gray-400">|</span>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default function ChatContent({ messages }: ChatContentProps) {
  const { getUserInfo } = useChatStore();
  const userInfo = getUserInfo();

  // 滚动相关常量
  const BOTTOM_THRESHOLD = 10;

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  
  // 滚动状态管理
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [lastContentHeight, setLastContentHeight] = useState(0);

  // 判断是否接近底部的工具函数
  const isScrollNearBottom = useCallback((container: HTMLDivElement) => {
    const { scrollTop, scrollHeight, clientHeight } = container;
    // 添加1像素的容错范围，提高检测精确度
    return scrollHeight - scrollTop - clientHeight <= BOTTOM_THRESHOLD + 1;
  }, [BOTTOM_THRESHOLD]);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const nearBottom = isScrollNearBottom(container);
    setIsNearBottom(nearBottom);
  }, [isScrollNearBottom]);

  // 智能滚动到底部函数
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container || isUserScrolling) return;
    
    // 检查是否真的需要滚动
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtBottom = scrollHeight - scrollTop - clientHeight <= BOTTOM_THRESHOLD;
    
    if (!isAtBottom) {
      container.scrollTop = container.scrollHeight;
    }
  }, [isUserScrolling, BOTTOM_THRESHOLD]);

  // 添加滚动事件监听器
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // 用户交互事件监听
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleUserInteraction = () => {
      setIsUserScrolling(true);
    };

    // 监听所有可能的用户交互
    container.addEventListener('wheel', handleUserInteraction);
    container.addEventListener('mousedown', handleUserInteraction);
    container.addEventListener('touchstart', handleUserInteraction);
    container.addEventListener('keydown', handleUserInteraction);
    
    return () => {
      container.removeEventListener('wheel', handleUserInteraction);
      container.removeEventListener('mousedown', handleUserInteraction);
      container.removeEventListener('touchstart', handleUserInteraction);
      container.removeEventListener('keydown', handleUserInteraction);
    };
  }, []);

  // 底部停留恢复机制
  useEffect(() => {
    if (!isUserScrolling || !isNearBottom) return;

    const timer = setTimeout(() => {
      setIsUserScrolling(false);
    }, 2000); // 在底部停留2秒后恢复

    return () => clearTimeout(timer);
  }, [isUserScrolling, isNearBottom]);

  // 内容高度变化监听
  useEffect(() => {
    const contentElement = contentRef.current;
    if (!contentElement) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const newHeight = entry.contentRect.height;
        if (newHeight > lastContentHeight && !isUserScrolling) {
          // 内容高度增加且用户未滚动时才滚动
          scrollToBottom();
        }
        setLastContentHeight(newHeight);
      }
    });

    resizeObserver.observe(contentElement);
    return () => resizeObserver.disconnect();
  }, [lastContentHeight, isUserScrolling, scrollToBottom]);

  // 组件初始化滚动
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 仅在组件挂载时执行

  if (messages.length === 0) return null;

  return (
    //对话内容区域
    <div ref={scrollContainerRef} className="flex-1 overflow-y-auto">
      <div ref={contentRef} className="max-w-4xl mx-auto px-8 py-6 space-y-6">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-4 ${
              message.type === "user" ? "justify-end" : "justify-start"
            }`}
          >
            {message.type === "user" ? (
              <>
                <div className="flex-1 flex justify-end">
                  <div className="bg-blue-500 text-white rounded-2xl px-4 py-3 inline-block max-w-[80%]">
                    <div className="text-white prose prose-invert max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeHighlight, rehypeRaw]}
                        components={userComponents}
                      >
                        {message.content}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
                <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm font-medium">{userInfo.realName?.slice(0, 1)}</span>
                </div>
              </>
            ) : (
              <>
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Image
                    src="/logo.svg"
                    width={16}
                    height={16}
                    alt="AI"
                    className="w-4 h-4 text-white"
                  />
                </div>
                <div className="flex-1">
                  <div className=" font-bold  text-black/85 text-2xl">AI点将</div>
                  {message.isStreaming ? (
                    <StreamingMessage message={message} />
                  ) : (
                    <StaticMessage message={message} />
                  )}
                </div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

