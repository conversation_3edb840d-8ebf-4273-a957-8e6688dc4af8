"use client";

import React, { useEffect, useState } from "react";
import { getRecQuestion } from "@/services/chatService";

interface ReQuestionProps {
  setInputValue: (message: string) => void;
}
interface RecQuestionResponse {
  data: string[];
}

const ChatRecQuestion = ({ setInputValue }: ReQuestionProps) => {
  const [recQuestion, setRecQuestion] = useState<string[]>([]);

  useEffect(() => {
    const fetchRecQuestion = async () => {
      const res = (await getRecQuestion()) as RecQuestionResponse;
      setRecQuestion(res.data);
    };
    fetchRecQuestion();
  }, []);

  const handleSetMessage = (message: string) => {
    setInputValue(message)
  };

  return (
    <>
      <div className="flex flex-wrap w-full mt-2 text-sm">
        {recQuestion.length > 0 &&
          recQuestion.map((item, index) => (
            // 推荐问题
            <div
              className={`flex items-center rounded-[80px] bg-white box-border border border-black/6 mt-4 px-6 py-3 mr-4 cursor-pointer hover:bg-blue-100 hover:text-blue-600`}
              key={index}
              onClick={() => handleSetMessage(item)}
            >
              {item}
            </div>
          ))}
      </div>
    </>
  );
};

export default ChatRecQuestion;
