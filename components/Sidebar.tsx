"use client";

import Image from "next/image";
import { PanelLeftClose, PanelLeftOpen } from "lucide-react";
import { useChatStore } from "@/store/chatStore";
import HistorySession from "./HistorySession";

interface SidebarProps {
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
}

export default function Sidebar({
  sidebarCollapsed,
  setSidebarCollapsed,
}: SidebarProps) {
  const { startNewChat, getUserInfo } = useChatStore();

  const handleNewChat = () => {
    startNewChat();
  };

  const userInfo = getUserInfo();

  return (
    <div
      className={`${
        sidebarCollapsed ? "w-16" : "w-64"
      } bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out`}
    >
      <div
        className={`p-4 border-b border-gray-200 flex flex-col ${
          sidebarCollapsed && "items-center"
        }`}
      >
        <div className="flex items-center justify-between mb-4">
          <div
            className={`flex items-center gap-2 ${
              sidebarCollapsed ? "justify-center" : ""
            }`}
          >
            {!sidebarCollapsed ? (
              <Image
                src="/logo-lg.svg"
                width={0}
                height={0}
                alt=""
                className="w-32 h-8"
              />
            ) : (
              <Image
                src="/logo.svg"
                width={0}
                height={0}
                alt=""
                className="w-6 h-6.5"
              />
            )}
          </div>
        </div>

        <button
          onClick={handleNewChat}
          className={`w-full flex items-center gap-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors ${
            sidebarCollapsed ? "justify-center" : "px-3 py-2"
          }`}
          title={sidebarCollapsed ? "新建会话" : ""}
        >
          <Image
            src="/add.svg"
            alt=""
            width={0}
            height={0}
            className="h-4 w-4"
          />
          {!sidebarCollapsed && <span>新建会话</span>}
        </button>
      </div>
      <HistorySession sidebarCollapsed={sidebarCollapsed} />

      <div className="p-4 border-t border-gray-200 bg-white">
        <div
          className={`flex items-center gap-3 ${
            sidebarCollapsed ? "flex-col-reverse " : ""
          }`}
        >
          <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {userInfo.realName?.slice(0, 1)}
            </span>
          </div>
          {!sidebarCollapsed && (
            <div className="text-sm text-gray-700 flex flex-1 ">
              {userInfo.realName}
            </div>
          )}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors flex-shrink-0"
              aria-label={sidebarCollapsed ? "展开侧边栏" : "收起侧边栏"}
            >
              {!sidebarCollapsed ? (
                <PanelLeftClose className="w-6 h-6 hover:text-blue-400" />
              ) : (
                <PanelLeftOpen className="w-6 h-6 hover:text-blue-400" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
