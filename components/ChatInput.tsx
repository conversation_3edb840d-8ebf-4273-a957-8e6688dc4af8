"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import { ArrowUp, Loader2, <PERSON>c<PERSON>, Square } from "lucide-react";
import { toast } from "sonner";
import {
  sendMessageStream,
  getGenTitle,
  saveHistory,
  fileParseUpload,
} from "@/services/chatService";
import type { SendMessageRequest } from "@/services/chatService";
import { useChatStore, type CurrentFile } from "@/store/chatStore";
import ChatRecQuestion from "./ChatRecQuestion";
import Image from "next/image";
import { formatFileSize } from "@/utils";

interface ChatInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  hasMessages: boolean;
  isLoading?: boolean;
}

interface GenTitleResponse {
  data: {
    title: string;
  };
}

interface SaveHistoryResponse {
  data: string;
}



interface FileParseResponse {
  data: string;
}

export default function ChatInput({
  inputValue,
  setInputValue,
  hasMessages,
  isLoading = false,
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 从chatStore获取流式数据处理方法
  const {
    startStreamingMessage,
    updateThinkingStream,
    completeThinkingStream,
    addTempMessage,
    currentStreamingMessage,
    messages,
    currentChatId,
    userInfo,
    isDeepMindEnabled,
    setIsDeepMindEnabled,
    uploadResult,
    addUploadFiles,
    removeUploadFile,
    clearUploadResult,
    sessionStreamingStates,
    setSessionStreamingState,
  } = useChatStore();

  // 简化的状态管理 - 支持多会话AbortController
  const [sessionAbortControllers, setSessionAbortControllers] =
    useState<Record<string, AbortController>>({});
  const [isPreparing, setIsPreparing] = useState<boolean>(false);

  // 文件上传状态
  const [isUploading, setIsUploading] = useState<boolean>(false);

  // 全局ID生成器
  const idCounterRef = useRef(0);
  const generateUniqueId = useCallback(() => {
    return Date.now() + (++idCounterRef.current);
  }, []);

  // 是否正在流式处理
  const isStreaming = !!currentStreamingMessage;

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.overflowY = "hidden";

      const lineHeight = 18;
      const verticalPadding = 28;
      const minHeight = 64;
      const maxHeight = lineHeight * 10 + verticalPadding;

      const scrollHeight = textarea.scrollHeight;
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);

      textarea.style.height = `${newHeight}px`;

      if (scrollHeight > maxHeight) {
        textarea.style.overflowY = "auto";
      } else {
        textarea.style.overflowY = "hidden";
      }
    }
  }, []);

  // 停止流式处理 - 支持多会话
  const handleStopStreaming = useCallback((sessionId?: string) => {
    const targetSessionId = sessionId || currentChatId;
    if (!targetSessionId) return;

    // 停止指定会话的请求
    const controller = sessionAbortControllers[targetSessionId];
    if (controller) {
      controller.abort();
      setSessionAbortControllers(prev => {
        const updated = { ...prev };
        delete updated[targetSessionId];
        return updated;
      });
    }

    // 停止UI层的流式消息处理
    const { stopStreamingMessage } = useChatStore.getState();
    stopStreamingMessage(sessionId);
  }, [sessionAbortControllers, currentChatId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    adjustTextareaHeight();
  };

  const handleSubmit = useCallback(
    async (value = inputValue) => {
      if (value.trim() && !isLoading && !isPreparing && !isStreaming) {
        const message = value.trim();
        // Clear input
        setInputValue("");
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.style.height = "auto";
            textareaRef.current.style.overflowY = "hidden";
            adjustTextareaHeight();
          }
        }, 0);

        setIsPreparing(true);
        try {
          let user_session_id: string;

          if (!hasMessages) {
            // This is a new chat session
            const {
              data: { title },
            } = (await getGenTitle({ query: message })) as GenTitleResponse;


            const { data: new_session_id } = (await saveHistory({
              sessionTitle: title,
              firstQuestion: message,
              data: JSON.stringify(uploadResult),

            })) as SaveHistoryResponse;

            user_session_id = new_session_id;

            // 立即更新currentChatId，确保后续的流式处理能正确关联到这个会话
            useChatStore.setState({
              currentChatId: new_session_id,
              currentTitle: title,
              chatHistory: [
                {
                  id: new_session_id,
                  sessionTitle: title,
                  firstQuestion: message,
                },
                ...useChatStore.getState().chatHistory,
              ],
            });
          } else {
            // This is an existing chat session
            user_session_id = currentChatId!;
            await saveHistory({
              id: currentChatId!,
              data: JSON.stringify(uploadResult),
            })
          }

          // 创建AbortController用于取消请求
          const controller = new AbortController();
          setSessionAbortControllers(prev => ({
            ...prev,
            [user_session_id]: controller
          }));

          // 同时保存到会话状态中
          const currentSessionState = sessionStreamingStates[user_session_id] || { messages: [] };
          setSessionStreamingState(user_session_id, {
            ...currentSessionState,
            abortController: controller
          });

          // 跟踪思考流是否已完成
          let thinkingStreamCompleted = false;

          // 在获取到session_id后立即开始流式消息处理
          startStreamingMessage(message, user_session_id);

          // 清空上传结果
          clearUploadResult();

          // 创建消息数据
          const messageData: SendMessageRequest = {
            query: message,
            user_session_id: `${user_session_id}`,
            enable_reasoning: isDeepMindEnabled,
            attachment_list: uploadResult && uploadResult.length > 0 ? uploadResult.map((item) => ({
              ftype: item.fileType,
              content: item.attachmentText,
              fname: item.name
            })) : undefined,
          };
          let isFirstChunkReceived = false;

          // 开始流式数据处理
          await sendMessageStream(
            messageData,
            // onData回调 - 处理每个数据块
            (chunk: string) => {
              if (!isFirstChunkReceived) {
                setIsPreparing(false);
                isFirstChunkReceived = true;
              }
              console.log("收到流式数据:", chunk);

              try {
                const jsonData = JSON.parse(chunk);

                // 处理深度思考内容
                if (
                  jsonData.ReasoningMessages &&
                  jsonData.ReasoningMessages.content
                ) {
                  updateThinkingStream(jsonData.ReasoningMessages.content, user_session_id);
                }

                // 处理最终回答内容
                if (jsonData.Messages && jsonData.Messages.content) {
                  const content = jsonData.Messages.content;

                  // 这是第一个Messages数据块
                  if (!thinkingStreamCompleted) {
                    thinkingStreamCompleted = true; // 标记思考过程已（逻辑上）结束

                    // 首先，将收到的内容暂存
                    addTempMessage(content, user_session_id);

                    // 检查是否存在思考内容，以决定下一步操作
                    const {
                      getSessionStreamingState,
                      completeThinkingStream,
                      startAnswerProcessing,
                    } = useChatStore.getState();

                    const sessionState = getSessionStreamingState(user_session_id);
                    const streamingMessage = sessionState?.streamingMessage;

                    if (streamingMessage?.thinkingContent) {
                      // 如果有思考内容，则正常结束思考流，这将自动触发答案处理
                      console.log("有思考内容，调用 completeThinkingStream");
                      completeThinkingStream(user_session_id);
                    } else {
                      // 如果没有思考内容（直接回答），则手动启动答案处理
                      console.log("无思考内容，直接调用 startAnswerProcessing");
                      startAnswerProcessing(user_session_id);
                    }
                  } else {
                    // 对于后续的Messages数据块，直接暂存/添加即可
                    addTempMessage(content, user_session_id);
                  }
                }
              } catch (parseError) {
                console.error("解析流式数据失败:", parseError);
              }
            },
            // onError回调 - 处理错误
            (error: Error) => {
              setIsPreparing(false);
              console.error("流式数据错误:", error);
              // 确保即使出错也完成流
              const { completeAnswerStream } = useChatStore.getState();
              completeAnswerStream(user_session_id);

              // 清理会话的AbortController
              setSessionAbortControllers(prev => {
                const updated = { ...prev };
                delete updated[user_session_id];
                return updated;
              });
            },
            // onComplete回调 - 处理完成
            () => {
              setIsPreparing(false);
              const { getSessionStreamingState, completeAnswerStream, completeThinkingStream } =
                useChatStore.getState();

              // 确保在流结束后，即使没有Messages数据，思考过程也被标记为完成
              if (!thinkingStreamCompleted) {
                const sessionState = getSessionStreamingState(user_session_id);
                const streamingMessage = sessionState?.streamingMessage;
                if (streamingMessage?.thinkingContent) {
                  completeThinkingStream(user_session_id);
                }
              }

              // 完成答案流
              completeAnswerStream(user_session_id);

              // 清理会话的AbortController
              setSessionAbortControllers(prev => {
                const updated = { ...prev };
                delete updated[user_session_id];
                return updated;
              });
            },
            // onAbort回调 - 处理用户取消
            () => {
              setIsPreparing(false);
              console.log("用户已取消流式请求");

              // 清理会话的AbortController
              setSessionAbortControllers(prev => {
                const updated = { ...prev };
                delete updated[user_session_id];
                return updated;
              });
            },
            // AbortSignal - 用于取消请求
            controller.signal
          );
        } catch (error) {
          console.error("启动流式处理失败:", error);
          setIsPreparing(false);
          handleStopStreaming();
        }
      }
    },
    [
      inputValue,
      isLoading,
      isPreparing,
      isStreaming,
      setInputValue,
      adjustTextareaHeight,
      hasMessages,
      currentChatId,
      startStreamingMessage,
      sessionStreamingStates,
      setSessionStreamingState,
      setSessionAbortControllers,
      isDeepMindEnabled,
      updateThinkingStream,
      addTempMessage,
      completeThinkingStream,
      handleStopStreaming,
      uploadResult,
      clearUploadResult,
    ]
  );

  // 删除文件处理函数
  const handleRemoveFile = useCallback((id: number) => {
    removeUploadFile(id);
  }, [removeUploadFile]);

  // 处理多文件上传
  const handleMultipleFileUpload = useCallback(
    async (files: FileList) => {
      const fileArray = Array.from(files);
      const validFiles: File[] = [];
      const invalidFiles: string[] = [];

      // 文件类型和大小预检查
      const documentTypes = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-powerpoint",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ];

      const imageTypes = ["image/png", "image/jpeg", "image/jpg"];

      const allowedTypes = [...documentTypes, ...imageTypes];
      const maxSize = 10 * 1024 * 1024;

      // 验证所有文件
      fileArray.forEach((file) => {
        if (file.size > maxSize) {
          invalidFiles.push(`${file.name}: 文件大小超过10MB`);
        } else if (!allowedTypes.includes(file.type)) {
          invalidFiles.push(`${file.name}: 不支持的文件类型`);
        } else {
          validFiles.push(file);
        }
      });

      // 显示错误信息
      if (invalidFiles.length > 0) {
        toast.error(
          <div className="text-sm">
            <p className="font-bold mb-1">以下文件无法上传:</p>
            <ul className="list-disc list-inside text-xs">
              {invalidFiles.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        );
      }

      // 如果没有有效文件，直接返回
      if (validFiles.length === 0) {
        return;
      }

      setIsUploading(true);

      // 为所有文件预分配唯一ID
      const fileIds = validFiles.map(() => generateUniqueId());

      // 并发上传所有有效文件
      const uploadPromises = validFiles.map(async (file, index) => {
        try {
          const res = await fileParseUpload(file) as FileParseResponse;
          const uploadingFile: CurrentFile = {
            id: fileIds[index],
            name: file.name,
            size: formatFileSize(file.size),
            type: documentTypes.includes(file.type) ? "/upfile.svg" : "/tp.svg",
            fileType: file.name.split(".").pop()?.toUpperCase() as
              | "PDF"
              | "DOC"
              | "DOCX"
              | "XLS"
              | "XLSX"
              | "PPT"
              | "PPTX"
              | "PNG"
              | "JPG"
              | "JPEG",
            attachmentText: res.data,
          };
          return { success: true, file: uploadingFile, error: null };
        } catch (error) {
          console.error(`文件上传失败 ${file.name}:`, error);
          return {
            success: false,
            file: null,
            error: `${file.name}: ${
              error instanceof Error ? error.message : "上传失败"
            }`,
          };
        }
      });

      try {
        const results = await Promise.allSettled(uploadPromises);
        console.log(results)
        const successfulUploads: CurrentFile[] = [];
        const failedUploads: string[] = [];

        results.forEach((result) => {
          if (result.status === "fulfilled") {
            const uploadResult = result.value;
            if (uploadResult.success && uploadResult.file) {
              successfulUploads.push(uploadResult.file);
            } else if (uploadResult.error) {
              failedUploads.push(uploadResult.error);
            }
          } else {
            failedUploads.push(`上传过程中发生错误: ${result.reason}`);
          }
        });

        // 更新成功上传的文件列表
        if (successfulUploads.length > 0) {
          addUploadFiles(successfulUploads);
          console.log(`成功上传 ${successfulUploads.length} 个文件`);
        }

        // 显示失败信息
        if (failedUploads.length > 0) {
          toast.error(`部分文件上传失败: ${failedUploads.join(", ")}`);
        }
      } catch (error) {
        console.error("批量上传过程中发生错误:", error);
        toast.error("批量上传失败，请重试");
      } finally {
        setIsUploading(false);
      }
    },
    [generateUniqueId, addUploadFiles]
  );

  // 触发文件选择
  const triggerFileUpload = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // 处理文件选择变化
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleMultipleFileUpload(files);
      }
      // 清空input，允许重复选择同一文件
      e.target.value = "";
    },
    [handleMultipleFileUpload]
  );

  useEffect(() => {
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  // 监听新建会话，清空附件列表
  useEffect(() => {
    if (currentChatId === "") {
      clearUploadResult();
    }
  }, [currentChatId, clearUploadResult]);

  // 清理AbortController
  useEffect(() => {
    return () => {
      // 清理所有会话的AbortController
      Object.values(sessionAbortControllers).forEach(controller => {
        controller.abort();
      });
    };
  }, [sessionAbortControllers]);

  const isSendButtonDisabled =
    isPreparing ||
    (isUploading && inputValue.trim().length > 0) ||
    (!isStreaming && inputValue.trim().length === 0);

  return (
    <div className={`flex flex-col py-4`}>
      {/* 主标语 - 上方居中显示 */}
      {!hasMessages && (
        <div className="flex items-center justify-center mb-12">
          <div className="text-center">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-purple-700 bg-clip-text text-transparent">
              慧眼识才，领航未来
            </h1>
          </div>
        </div>
      )}

      {/* 用户问候和输入框区域 */}
      <div className="pb-2">
        <div className="max-w-4xl mx-auto">
          {/* 用户问候文字 - 紧贴输入框上方 */}
          {!hasMessages && (
            <div className="text-3xl font-bold text-gray-800 mb-4">
              {userInfo.nickName}{userInfo.gender === 1 ? '先生':'女士'}
            </div>
          )}
          <div className="flex flex-col border border-gray-300 rounded-3xl bg-white">
            {uploadResult.length > 0 && (
              <div className="max-h-[180px] overflow-y-auto overflow-x-hidden custom-scrollbar">
                <div className="text-xs ml-2 mt-2 text-gray-500">
                  仅识别附件中的文字
                </div>
                <div className="grid grid-cols-4 gap-4 p-2">
                  {/* 附件列表 */}
                  {uploadResult.map((item) => (
                    <div
                      className="flex items-center p-2 gap-2 rounded-xl transition-colors duration-300 bg-gray-100 relative cursor-pointer group min-w-0"
                      key={item.id}
                    >
                      <Image
                        src={item.type}
                        alt=""
                        width={0}
                        height={0}
                        className="w-6 h-6"
                      />
                      <div className="flex flex-col justify-center min-w-0 flex-1">
                        <span className="text-sm font-bold truncate">
                          {item.name}
                        </span>
                        <span className="text-xs text-gray-500">
                          {item.fileType} {item.size}
                        </span>
                      </div>
                      {/* 删除按钮 */}
                      <div
                        className="cursor-pointer border border-gray-200 bg-white justify-center items-center w-5 h-5 flex absolute -top-[5px] -right-[5px] rounded-full group-hover:opacity-100 opacity-0 transition-opacity duration-200 ease-in-out"
                        onClick={() => handleRemoveFile(item.id)}
                      >
                        <Image
                          src="/close.svg"
                          alt=""
                          width={0}
                          height={0}
                          className="w-2 h-2"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 输入框 */}
            <div className="relative flex bg-white flex-col border border-gray-300 rounded-3xl focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-transparent shadow-sm transition-all py-1.5 px-2 min-h-[110px]">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit();
                  }
                }}
                placeholder={
                  isLoading || isStreaming
                    ? "AI 正在思考中..."
                    : "说出你需要的人才类型..."
                }
                className="w-full rounded-xl resize-none leading-6 custom-scrollbar custom-textarea bg-transparent border-none outline-none px-4 "
                disabled={isLoading || isStreaming}
              />
              <div className="flex w-full items-center justify-between px-2 mb-1.5">
                {/* #4D6BFE */}
                <button
                  type="button"
                  aria-label="深度思考"
                  className={`cursor-pointer gap-1 flex items-center py-2 px-4 text-sm box-border border border-black/6 rounded-4xl ${
                    isDeepMindEnabled
                      ? "text-[#4D6BFE] hover:bg-[#C3DAF8] bg-[#DBEAFE]"
                      : "hover:bg-[#F6F6F8] bg-white"
                  }`}
                  onClick={() => setIsDeepMindEnabled(!isDeepMindEnabled)}
                >
                  <Image
                    src={`/deepmind${isDeepMindEnabled ? "-active" : ""}.svg`}
                    alt="深度思考"
                    width={16}
                    height={16}
                  />
                  <span>深度思考</span>
                </button>
                <div className="flex flex-1 gap-2 items-center justify-end">
                  {/* 隐藏的文件输入 */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    onChange={handleFileChange}
                    style={{ display: "none" }}
                    accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.png,.jpg,.jpeg"
                    multiple
                  />
                  {/* 上传文件按钮 */}
                  <div className="relative group">
                    {/* Tooltip */}
                    <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 w-max bg-gray-800 text-white rounded-md px-3 py-2 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="text-sm font-medium">上传附件（仅识别文字）</div>
                      <div className="text-xs text-gray-300 mt-1">每个支持10 MB，支持各类文档和图片</div>
                      {/* 指向下方的小三角 */}
                      <div className="absolute left-1/2 -translate-x-1/2 bottom-[-4px] w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-gray-800"></div>
                    </div>
                    <button
                      type="button"
                      className={`rounded-full h-9 w-9 flex items-center justify-center p-2 transition-colors cursor-pointer ${
                        isUploading || isPreparing
                          ? "text-gray-400 bg-[#F6F6F8]"
                          : "text-black hover:bg-[#F6F6F8]"
                      }`}
                      onClick={triggerFileUpload}
                      disabled={isUploading || isPreparing}
                      aria-label={isUploading ? "正在上传..." : "上传文件"}
                    >
                      {isUploading ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Paperclip />
                      )}
                    </button>
                  </div>
                  {/* 发送按钮 */}
                  <div className="relative group">
                    {/* 上传文件时的tooltip */}
                    {isUploading && inputValue.trim().length > 0 && (
                      <div className="absolute bottom-full mb-2 w-max bg-gray-800 text-white text-sm rounded-md px-3 py-1.5 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        文件解析中...
                        <div className="absolute left-1/2 -translate-x-1/2 bottom-[-4px] w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-gray-800"></div>
                      </div>
                    )}
                    {/* 默认状态tooltip */}
                    {isSendButtonDisabled && inputValue.trim().length === 0 && !isUploading && !isPreparing && (
                      <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 w-max bg-gray-800 text-white text-sm rounded-md px-3 py-1.5 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        请输入你的问题
                        <div className="absolute left-1/2 -translate-x-1/2 bottom-[-4px] w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-gray-800"></div>
                      </div>
                    )}
                    <button
                      type="button"
                      className={`rounded-full h-9 w-9 flex items-center justify-center p-2 transition-colors ${
                        isSendButtonDisabled
                          ? "text-gray-400 bg-[#F6F6F8] cursor-not-allowed"
                          : isStreaming
                          ? "text-white bg-blue-600 hover:bg-blue-800 cursor-pointer"
                          : "text-white bg-blue-400 hover:bg-blue-500 cursor-pointer"
                      }`}
                      onClick={
                        isStreaming && !isPreparing
                          ? handleStopStreaming
                          : () => handleSubmit()
                      }
                      disabled={isSendButtonDisabled}
                      aria-label={
                        isPreparing
                          ? "正在处理..."
                          : isStreaming
                          ? "停止"
                          : "发送消息"
                      }
                    >
                      {isPreparing ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : isStreaming ? (
                        <Square
                          className="w-3 h-3 bg-white rounded-xs"
                          color="white"
                        />
                      ) : (
                        <ArrowUp className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {messages.length === 0 && (
            <ChatRecQuestion setInputValue={setInputValue} />
          )}
        </div>
      </div>
    </div>
  );
}
