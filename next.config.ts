import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'standalone',
  // 服务器外部包配置 (Next.js 15+)
  serverExternalPackages: [],

  // API重写规则 - 代理到后端API
  async rewrites() {
    return [
      {
        source: '/api/proxy/:path*',
        destination: `${process.env.NEXT_PUBLIC_BACKEND_API_URL}/:path*`,
      },
      // Removed chat-proxy rewrite to allow API route handling
    ];
  },
  // 环境变量配置
  env: {
    NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL,
    // NEXT_PUBLIC_CHAT_API_URL removed - using server-side proxy instead
  },
};

export default nextConfig;
